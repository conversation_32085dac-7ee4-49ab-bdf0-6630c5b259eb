{
  // GitHub Copilot Settings
  "github.copilot.enable": {
    "*": false
  },
  
  // Copilot Chat Settings
  "github.copilot.chat.enabled": true,
  "github.copilot.chat.welcomeMessage": "never",
  
  // Editor Settings for better Copilot experience
  "editor.inlineSuggest.enabled": true,
  "editor.inlineSuggest.showToolbar": "onHover",
  "editor.suggest.preview": true,
  "editor.acceptSuggestionOnCommitCharacter": false,
  "editor.acceptSuggestionOnEnter": "on",
  
  // TypeScript/JavaScript specific
  "typescript.suggest.autoImports": true,
  "javascript.suggest.autoImports": true,
  
  // React/React Native specific
  "emmet.includeLanguages": {
    "javascript": "javascriptreact",
    "typescript": "typescriptreact"
  },
  
  // File associations
  "files.associations": {
    "*.tsx": "typescriptreact",
    "*.jsx": "javascriptreact"
  },
  
  // Copilot keybindings (optional)
  "editor.tabCompletion": "on"
}
