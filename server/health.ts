// Voeg dit toe aan je ontwikkelserver
import { Request, Response } from 'express';
import fs from 'fs/promises';
import path from 'path';
import { FileSystemManager } from './utils/FileSystemManager';
import { FigmaIntegration } from './utils/FigmaIntegration';

interface HealthCheckResult {
  status: 'ok' | 'warning' | 'error';
  timestamp: string;
  server: string;
  checks: {
    filesystem: SystemCheck;
    figma: SystemCheck;
    permissions: SystemCheck;
    projects: SystemCheck;
  };
  errors?: string[];
  warnings?: string[];
}

interface SystemCheck {
  status: 'ok' | 'warning' | 'error';
  message: string;
  details?: any;
}

// Basis health check
app.get('/health', async (req: Request, res: Response) => {
  const healthResult: HealthCheckResult = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    server: 'development',
    checks: {
      filesystem: { status: 'ok', message: 'Not checked' },
      figma: { status: 'ok', message: 'Not checked' },
      permissions: { status: 'ok', message: 'Not checked' },
      projects: { status: 'ok', message: 'Not checked' }
    },
    errors: [],
    warnings: []
  };

  try {
    // Uitgebreide health check als query parameter
    if (req.query.detailed === 'true') {
      const fileSystemManager = FileSystemManager.getInstance();
      const figmaIntegration = FigmaIntegration.getInstance();

      // Filesystem check
      healthResult.checks.filesystem = await performFilesystemCheck();

      // Figma integration check
      healthResult.checks.figma = await performFigmaCheck(figmaIntegration);

      // Permissions check
      healthResult.checks.permissions = await performPermissionsCheck();

      // Projects check
      healthResult.checks.projects = await performProjectsCheck(fileSystemManager);

      // Bepaal overall status
      const allChecks = Object.values(healthResult.checks);
      const hasErrors = allChecks.some(check => check.status === 'error');
      const hasWarnings = allChecks.some(check => check.status === 'warning');

      if (hasErrors) {
        healthResult.status = 'error';
        healthResult.errors = allChecks
          .filter(check => check.status === 'error')
          .map(check => check.message);
      } else if (hasWarnings) {
        healthResult.status = 'warning';
        healthResult.warnings = allChecks
          .filter(check => check.status === 'warning')
          .map(check => check.message);
      }
    }

    res.json(healthResult);
  } catch (error) {
    console.error('Health check failed:', error);
    res.status(500).json({
      status: 'error',
      timestamp: new Date().toISOString(),
      server: 'development',
      error: 'Health check failed',
      details: error.message
    });
  }
});

async function performFilesystemCheck(): Promise<SystemCheck> {
  try {
    const projectsDir = path.join(process.cwd(), 'projects');
    const tempDir = path.join(process.cwd(), 'temp');

    // Controleer of directories bestaan en toegankelijk zijn
    await fs.access(projectsDir, fs.constants.R_OK | fs.constants.W_OK);
    await fs.access(tempDir, fs.constants.R_OK | fs.constants.W_OK);

    return {
      status: 'ok',
      message: 'Filesystem toegankelijk',
      details: { projectsDir, tempDir }
    };
  } catch (error) {
    return {
      status: 'error',
      message: `Filesystem probleem: ${error.message}`,
      details: { error: error.code }
    };
  }
}

async function performFigmaCheck(figmaIntegration: FigmaIntegration): Promise<SystemCheck> {
  try {
    const isConnected = await figmaIntegration.testConnection();
    const tokenStatus = await figmaIntegration.validateToken();

    if (!isConnected) {
      return {
        status: 'error',
        message: 'Figma API niet bereikbaar',
        details: { connected: false, tokenValid: tokenStatus }
      };
    }

    if (!tokenStatus) {
      return {
        status: 'warning',
        message: 'Figma token verlopen of ongeldig',
        details: { connected: true, tokenValid: false }
      };
    }

    return {
      status: 'ok',
      message: 'Figma integratie operationeel',
      details: { connected: true, tokenValid: true }
    };
  } catch (error) {
    return {
      status: 'error',
      message: `Figma check gefaald: ${error.message}`
    };
  }
}

async function performPermissionsCheck(): Promise<SystemCheck> {
  try {
    const testFile = path.join(process.cwd(), 'temp', 'permission_test.tmp');

    // Test schrijfrechten
    await fs.writeFile(testFile, 'test');
    await fs.readFile(testFile);
    await fs.unlink(testFile);

    return {
      status: 'ok',
      message: 'Bestandsmachtigingen correct'
    };
  } catch (error) {
    return {
      status: 'error',
      message: `Machtigingsprobleem: ${error.message}`
    };
  }
}

async function performProjectsCheck(fileSystemManager: FileSystemManager): Promise<SystemCheck> {
  try {
    const projectsStatus = await fileSystemManager.validateAllProjects();
    const corruptProjects = projectsStatus.filter(p => p.status === 'corrupt');
    const inaccessibleProjects = projectsStatus.filter(p => p.status === 'inaccessible');

    if (corruptProjects.length > 0 || inaccessibleProjects.length > 0) {
      return {
        status: 'warning',
        message: `${corruptProjects.length} corrupte en ${inaccessibleProjects.length} ontoegankelijke projecten gevonden`,
        details: { corrupt: corruptProjects, inaccessible: inaccessibleProjects }
      };
    }

    return {
      status: 'ok',
      message: `${projectsStatus.length} projecten gevalideerd`,
      details: { totalProjects: projectsStatus.length }
    };
  } catch (error) {
    return {
      status: 'error',
      message: `Project validatie gefaald: ${error.message}`
    };
  }
}
