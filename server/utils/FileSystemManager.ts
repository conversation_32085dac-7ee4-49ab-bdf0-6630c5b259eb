import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';
import { FigmaIntegration } from './FigmaIntegration';

export interface FileValidationResult {
  filePath: string;
  status: 'valid' | 'corrupt' | 'inaccessible' | 'incompatible' | 'missing_figma';
  error?: string;
  details?: {
    size?: number;
    lastModified?: Date;
    checksum?: string;
    version?: string;
    figmaFileId?: string;
    figmaLastSync?: Date;
  };
}

export interface ProjectValidationResult {
  projectId: string;
  projectName: string;
  status: 'valid' | 'corrupt' | 'inaccessible' | 'incompatible';
  files: FileValidationResult[];
  figmaStatus?: {
    connected: boolean;
    lastSync?: Date;
    hasChanges?: boolean;
  };
}

export class FileSystemManager {
  private static instance: FileSystemManager;
  private figmaIntegration: FigmaIntegration;
  private supportedVersions = ['1.0', '1.1', '1.2', '2.0'];
  private supportedExtensions = ['.json', '.figma', '.design', '.project'];

  private constructor() {
    this.figmaIntegration = FigmaIntegration.getInstance();
  }

  static getInstance(): FileSystemManager {
    if (!FileSystemManager.instance) {
      FileSystemManager.instance = new FileSystemManager();
    }
    return FileSystemManager.instance;
  }

  /**
   * Uitgebreide bestandsvalidatie met Figma integratie
   */
  async validateFile(filePath: string): Promise<FileValidationResult> {
    const result: FileValidationResult = {
      filePath,
      status: 'valid',
      details: {}
    };

    try {
      // Controleer of bestand bestaat
      const stats = await fs.stat(filePath);
      result.details!.size = stats.size;
      result.details!.lastModified = stats.mtime;

      // Controleer bestandsextensie
      const ext = path.extname(filePath).toLowerCase();
      if (!this.supportedExtensions.includes(ext)) {
        result.status = 'incompatible';
        result.error = `Niet-ondersteunde bestandsextensie: ${ext}`;
        return result;
      }

      // Lees en valideer bestandsinhoud
      const content = await fs.readFile(filePath, 'utf-8');
      
      // Bereken checksum
      result.details!.checksum = crypto
        .createHash('md5')
        .update(content)
        .digest('hex');

      // Valideer JSON structuur (voor .json en .project bestanden)
      if (['.json', '.project'].includes(ext)) {
        const validationResult = await this.validateJsonStructure(content, filePath);
        if (!validationResult.isValid) {
          result.status = 'corrupt';
          result.error = validationResult.error;
          return result;
        }
        
        result.details!.version = validationResult.version;
        result.details!.figmaFileId = validationResult.figmaFileId;
      }

      // Figma synchronisatie check
      if (result.details!.figmaFileId) {
        const figmaStatus = await this.checkFigmaSync(
          result.details!.figmaFileId,
          result.details!.lastModified!
        );
        
        if (!figmaStatus.accessible) {
          result.status = 'missing_figma';
          result.error = 'Gekoppeld Figma bestand niet toegankelijk';
        }
        
        result.details!.figmaLastSync = figmaStatus.lastSync;
      }

      return result;

    } catch (error) {
      if (error.code === 'ENOENT') {
        result.status = 'inaccessible';
        result.error = 'Bestand niet gevonden';
      } else if (error.code === 'EACCES') {
        result.status = 'inaccessible';
        result.error = 'Onvoldoende machtigingen';
      } else {
        result.status = 'corrupt';
        result.error = `Bestandsfout: ${error.message}`;
      }
      
      return result;
    }
  }

  /**
   * Valideer JSON bestandsstructuur
   */
  private async validateJsonStructure(content: string, filePath: string): Promise<{
    isValid: boolean;
    error?: string;
    version?: string;
    figmaFileId?: string;
  }> {
    try {
      const data = JSON.parse(content);
      
      // Controleer vereiste velden
      if (!data.version) {
        return {
          isValid: false,
          error: 'Ontbrekend versieveld in projectbestand'
        };
      }
      
      if (!this.supportedVersions.includes(data.version)) {
        return {
          isValid: false,
          error: `Niet-ondersteunde versie: ${data.version}. Ondersteund: ${this.supportedVersions.join(', ')}`
        };
      }
      
      // Valideer project structuur
      if (data.type === 'project') {
        const requiredFields = ['id', 'name', 'components'];
        for (const field of requiredFields) {
          if (!data[field]) {
            return {
              isValid: false,
              error: `Ontbrekend vereist veld: ${field}`
            };
          }
        }
      }
      
      return {
        isValid: true,
        version: data.version,
        figmaFileId: data.figma?.fileId
      };
      
    } catch (error) {
      return {
        isValid: false,
        error: `JSON parse fout: ${error.message}`
      };
    }
  }

  /**
   * Controleer Figma synchronisatie status
   */
  private async checkFigmaSync(figmaFileId: string, lastModified: Date): Promise<{
    accessible: boolean;
    lastSync?: Date;
    hasChanges?: boolean;
  }> {
    try {
      const figmaFile = await this.figmaIntegration.getFileInfo(figmaFileId);
      
      if (!figmaFile) {
        return { accessible: false };
      }
      
      const figmaLastModified = new Date(figmaFile.last_modified);
      const hasChanges = figmaLastModified > lastModified;
      
      return {
        accessible: true,
        lastSync: lastModified,
        hasChanges
      };
      
    } catch (error) {
      console.error(`Figma sync check failed for ${figmaFileId}:`, error);
      return { accessible: false };
    }
  }

  /**
   * Valideer alle projecten in de projectenmap
   */
  async validateAllProjects(): Promise<ProjectValidationResult[]> {
    const projectsDir = path.join(process.cwd(), 'projects');
    const results: ProjectValidationResult[] = [];
    
    try {
      const entries = await fs.readdir(projectsDir, { withFileTypes: true });
      
      for (const entry of entries) {
        if (entry.isDirectory()) {
          const projectResult = await this.validateProject(
            path.join(projectsDir, entry.name)
          );
          results.push(projectResult);
        }
      }
      
    } catch (error) {
      console.error('Failed to validate projects:', error);
    }
    
    return results;
  }

  /**
   * Valideer individueel project
   */
  async validateProject(projectPath: string): Promise<ProjectValidationResult> {
    const projectName = path.basename(projectPath);
    const result: ProjectValidationResult = {
      projectId: projectName,
      projectName,
      status: 'valid',
      files: []
    };

    try {
      const files = await fs.readdir(projectPath);
      
      for (const file of files) {
        const filePath = path.join(projectPath, file);
        const fileResult = await this.validateFile(filePath);
        result.files.push(fileResult);
      }
      
      // Bepaal project status op basis van bestanden
      const hasCorruptFiles = result.files.some(f => f.status === 'corrupt');
      const hasInaccessibleFiles = result.files.some(f => f.status === 'inaccessible');
      const hasIncompatibleFiles = result.files.some(f => f.status === 'incompatible');
      
      if (hasCorruptFiles) {
        result.status = 'corrupt';
      } else if (hasInaccessibleFiles) {
        result.status = 'inaccessible';
      } else if (hasIncompatibleFiles) {
        result.status = 'incompatible';
      }
      
      // Figma status voor project
      const figmaFiles = result.files.filter(f => f.details?.figmaFileId);
      if (figmaFiles.length > 0) {
        result.figmaStatus = {
          connected: figmaFiles.every(f => f.status !== 'missing_figma'),
          hasChanges: figmaFiles.some(f => f.details?.figmaLastSync)
        };
      }
      
    } catch (error) {
      result.status = 'inaccessible';
    }
    
    return result;
  }

  /**
   * Probeer automatisch herstel van corrupte bestanden
   */
  async attemptFileRecovery(filePath: string): Promise<{
    success: boolean;
    method?: string;
    error?: string;
  }> {
    try {
      // Probeer backup herstel
      const backupResult = await this.restoreFromBackup(filePath);
      if (backupResult.success) {
        return { success: true, method: 'backup_restore' };
      }
      
      // Probeer Figma re-sync
      const figmaResult = await this.restoreFromFigma(filePath);
      if (figmaResult.success) {
        return { success: true, method: 'figma_resync' };
      }
      
      return {
        success: false,
        error: 'Geen hersteloptie beschikbaar'
      };
      
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  private async restoreFromBackup(filePath: string): Promise<{ success: boolean }> {
    const backupPath = `${filePath}.backup`;
    
    try {
      await fs.access(backupPath);
      await fs.copyFile(backupPath, filePath);
      return { success: true };
    } catch {
      return { success: false };
    }
  }

  private async restoreFromFigma(filePath: string): Promise<{ success: boolean }> {
    try {
      // Lees figma file ID uit corrupt bestand (als mogelijk)
      const content = await fs.readFile(filePath, 'utf-8');
      const figmaFileId = this.extractFigmaFileId(content);
      
      if (figmaFileId) {
        const figmaData = await this.figmaIntegration.exportFile(figmaFileId);
        await fs.writeFile(filePath, JSON.stringify(figmaData, null, 2));
        return { success: true };
      }
      
      return { success: false };
    } catch {
      return { success: false };
    }
  }

  private extractFigmaFileId(content: string): string | null {
    try {
      const data = JSON.parse(content);
      return data.figma?.fileId || null;
    } catch {
      // Probeer regex extractie als JSON parsing faalt
      const match = content.match(/"figmaFileId":\s*"([^"]+)"/);
      return match ? match[1] : null;
    }
  }
}
