import axios, { AxiosInstance } from 'axios';

export interface FigmaFileInfo {
  key: string;
  name: string;
  last_modified: string;
  thumbnail_url: string;
  version: string;
}

export class FigmaIntegration {
  private static instance: FigmaIntegration;
  private apiClient: AxiosInstance;
  private accessToken: string;

  private constructor() {
    this.accessToken = process.env.FIGMA_ACCESS_TOKEN || '';
    this.apiClient = axios.create({
      baseURL: 'https://api.figma.com/v1',
      headers: {
        'X-Figma-Token': this.accessToken
      },
      timeout: 10000
    });
  }

  static getInstance(): FigmaIntegration {
    if (!FigmaIntegration.instance) {
      FigmaIntegration.instance = new FigmaIntegration();
    }
    return FigmaIntegration.instance;
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await this.apiClient.get('/me');
      return response.status === 200;
    } catch (error) {
      console.error('Figma connection test failed:', error.message);
      return false;
    }
  }

  async validateToken(): Promise<boolean> {
    if (!this.accessToken) {
      return false;
    }

    try {
      const response = await this.apiClient.get('/me');
      return response.status === 200 && response.data.id;
    } catch (error) {
      return false;
    }
  }

  async getFileInfo(fileId: string): Promise<FigmaFileInfo | null> {
    try {
      const response = await this.apiClient.get(`/files/${fileId}`);
      return {
        key: fileId,
        name: response.data.name,
        last_modified: response.data.lastModified,
        thumbnail_url: response.data.thumbnailUrl,
        version: response.data.version
      };
    } catch (error) {
      console.error(`Failed to get Figma file info for ${fileId}:`, error.message);
      return null;
    }
  }

  async exportFile(fileId: string): Promise<any> {
    try {
      const response = await this.apiClient.get(`/files/${fileId}`);
      return {
        id: fileId,
        name: response.data.name,
        lastModified: response.data.lastModified,
        version: response.data.version,
        document: response.data.document,
        figma: {
          fileId,
          lastSync: new Date().toISOString()
        }
      };
    } catch (error) {
      throw new Error(`Figma export failed: ${error.message}`);
    }
  }
}
