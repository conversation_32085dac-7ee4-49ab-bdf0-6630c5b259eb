import { Router, Request, Response } from 'express';
import { FileSystemManager } from '../utils/FileSystemManager';

const router = Router();
const fileSystemManager = FileSystemManager.getInstance();

// Valideer specifiek bestand
router.post('/validate-file', async (req: Request, res: Response) => {
  try {
    const { filePath } = req.body;
    
    if (!filePath) {
      return res.status(400).json({
        error: 'Bestandspad is vereist'
      });
    }

    const result = await fileSystemManager.validateFile(filePath);
    
    res.json({
      validation: result,
      recommendations: generateRecommendations(result)
    });
    
  } catch (error) {
    res.status(500).json({
      error: 'Bestandsvalidatie gefaald',
      details: error.message
    });
  }
});

// Probeer bestandsherstel
router.post('/recover-file', async (req: Request, res: Response) => {
  try {
    const { filePath } = req.body;
    
    const recoveryResult = await fileSystemManager.attemptFileRecovery(filePath);
    
    if (recoveryResult.success) {
      // Valideer herstel
      const validationResult = await fileSystemManager.validateFile(filePath);
      
      res.json({
        recovery: recoveryResult,
        validation: validationResult,
        message: `Bestand hersteld via ${recoveryResult.method}`
      });
    } else {
      res.status(422).json({
        recovery: recoveryResult,
        message: 'Bestandsherstel gefaald'
      });
    }
    
  } catch (error) {
    res.status(500).json({
      error: 'Herstelpoging gefaald',
      details: error.message
    });
  }
});

// Valideer alle projecten
router.get('/validate-projects', async (req: Request, res: Response) => {
  try {
    const results = await fileSystemManager.validateAllProjects();
    
    const summary = {
      total: results.length,
      valid: results.filter(p => p.status === 'valid').length,
      corrupt: results.filter(p => p.status === 'corrupt').length,
      inaccessible: results.filter(p => p.status === 'inaccessible').length,
      incompatible: results.filter(p => p.status === 'incompatible').length
    };
    
    res.json({
      summary,
      projects: results,
      recommendations: generateProjectRecommendations(results)
    });
    
  } catch (error) {
    res.status(500).json({
      error: 'Projectvalidatie gefaald',
      details: error.message
    });
  }
});

function generateRecommendations(result: any): string[] {
  const recommendations: string[] = [];
  
  switch (result.status) {
    case 'corrupt':
      recommendations.push('Probeer bestandsherstel via backup');
      recommendations.push('Controleer Figma synchronisatie');
      recommendations.push('Valideer JSON syntax handmatig');
      break;
      
    case 'inaccessible':
      recommendations.push('Controleer bestandsmachtigingen');
      recommendations.push('Verifieer dat het bestand bestaat');
      recommendations.push('Controleer netwerkverbinding (voor externe bestanden)');
      break;
      
    case 'incompatible':
      recommendations.push('Update naar ondersteunde bestandsversie');
      recommendations.push('Gebruik migratie tool voor legacy bestanden');
      break;
      
    case 'missing_figma':
      recommendations.push('Herstel Figma verbinding');
      recommendations.push('Update Figma access token');
      recommendations.push('Controleer Figma bestand toegang');
      break;
  }
  
  return recommendations;
}

function generateProjectRecommendations(results: any[]): string[] {
  const recommendations: string[] = [];
  
  const corruptProjects = results.filter(p => p.status === 'corrupt');
  const figmaIssues = results.filter(p => p.figmaStatus && !p.figmaStatus.connected);
  
  if (corruptProjects.length > 0) {
    recommendations.push(`${corruptProjects.length} projecten hebben corrupte bestanden - gebruik bulk herstel`);
  }
  
  if (figmaIssues.length > 0) {
    recommendations.push(`${figmaIssues.length} projecten hebben Figma synchronisatie problemen`);
  }
  
  return recommendations;
}

export default router;
