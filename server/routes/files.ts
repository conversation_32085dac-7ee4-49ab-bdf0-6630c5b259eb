import { Request, Response } from 'express';
import { FileSystemManager } from '../utils/FileSystemManager';
import path from 'path';
import fs from 'fs/promises';

const fileSystemManager = FileSystemManager.getInstance();

// GET /api/files - Lijst alle projecten
export const getProjects = async (req: Request, res: Response) => {
  try {
    const projects = await fileSystemManager.validateAllProjects();
    res.json({
      success: true,
      projects: projects.map(project => ({
        id: project.projectId,
        name: project.projectName,
        status: project.status,
        fileCount: project.files.length,
        figmaStatus: project.figmaStatus,
        lastModified: project.files.reduce((latest, file) => {
          const fileDate = file.details?.lastModified;
          return fileDate && (!latest || fileDate > latest) ? fileDate : latest;
        }, null as Date | null)
      }))
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Kon projecten niet ophalen',
      details: error.message
    });
  }
};

// GET /api/files/:projectId - Bestanden in een project
export const getProjectFiles = async (req: Request, res: Response) => {
  try {
    const { projectId } = req.params;
    const projectPath = path.join(process.cwd(), 'projects', projectId);
    
    const projectValidation = await fileSystemManager.validateProject(projectPath);
    
    res.json({
      success: true,
      project: {
        id: projectValidation.projectId,
        name: projectValidation.projectName,
        status: projectValidation.status,
        figmaStatus: projectValidation.figmaStatus
      },
      files: projectValidation.files.map(file => ({
        name: path.basename(file.filePath),
        path: file.filePath,
        status: file.status,
        size: file.details?.size,
        lastModified: file.details?.lastModified,
        version: file.details?.version,
        figmaFileId: file.details?.figmaFileId,
        figmaLastSync: file.details?.figmaLastSync,
        checksum: file.details?.checksum,
        error: file.error
      }))
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Kon projectbestanden niet ophalen',
      details: error.message
    });
  }
};

// GET /api/files/:projectId/:fileName - Bestandsinhoud
export const getFileContent = async (req: Request, res: Response) => {
  try {
    const { projectId, fileName } = req.params;
    const filePath = path.join(process.cwd(), 'projects', projectId, fileName);
    
    // Valideer bestand eerst
    const validation = await fileSystemManager.validateFile(filePath);
    
    if (validation.status === 'inaccessible') {
      return res.status(404).json({
        success: false,
        error: 'Bestand niet toegankelijk',
        details: validation.error
      });
    }
    
    const content = await fs.readFile(filePath, 'utf-8');
    
    res.json({
      success: true,
      file: {
        name: fileName,
        path: filePath,
        content,
        validation
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Kon bestandsinhoud niet ophalen',
      details: error.message
    });
  }
};

// POST /api/files/:projectId/:fileName/recover - Probeer bestand te herstellen
export const recoverFile = async (req: Request, res: Response) => {
  try {
    const { projectId, fileName } = req.params;
    const filePath = path.join(process.cwd(), 'projects', projectId, fileName);
    
    const recoveryResult = await fileSystemManager.attemptFileRecovery(filePath);
    
    if (recoveryResult.success) {
      // Valideer bestand na herstel
      const validation = await fileSystemManager.validateFile(filePath);
      
      res.json({
        success: true,
        message: 'Bestand succesvol hersteld',
        method: recoveryResult.method,
        validation
      });
    } else {
      res.status(400).json({
        success: false,
        error: 'Bestandsherstel gefaald',
        details: recoveryResult.error
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Herstelproces gefaald',
      details: error.message
    });
  }
};

// DELETE /api/files/:projectId/:fileName - Verwijder bestand
export const deleteFile = async (req: Request, res: Response) => {
  try {
    const { projectId, fileName } = req.params;
    const filePath = path.join(process.cwd(), 'projects', projectId, fileName);
    
    // Maak eerst een backup
    const backupPath = `${filePath}.backup.${Date.now()}`;
    await fs.copyFile(filePath, backupPath);
    
    // Verwijder origineel bestand
    await fs.unlink(filePath);
    
    res.json({
      success: true,
      message: 'Bestand verwijderd',
      backupPath
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Kon bestand niet verwijderen',
      details: error.message
    });
  }
};

// POST /api/files/:projectId - Maak nieuw bestand
export const createFile = async (req: Request, res: Response) => {
  try {
    const { projectId } = req.params;
    const { fileName, content = '', type = 'json' } = req.body;
    
    if (!fileName) {
      return res.status(400).json({
        success: false,
        error: 'Bestandsnaam is vereist'
      });
    }
    
    const projectPath = path.join(process.cwd(), 'projects', projectId);
    const filePath = path.join(projectPath, fileName);
    
    // Controleer of bestand al bestaat
    try {
      await fs.access(filePath);
      return res.status(409).json({
        success: false,
        error: 'Bestand bestaat al'
      });
    } catch {
      // Bestand bestaat niet, dit is goed
    }
    
    // Zorg ervoor dat project directory bestaat
    await fs.mkdir(projectPath, { recursive: true });
    
    // Maak template content op basis van type
    let fileContent = content;
    if (!content && type === 'json') {
      fileContent = JSON.stringify({
        version: '2.0',
        type: 'project',
        id: `${projectId}_${Date.now()}`,
        name: fileName.replace(/\.[^/.]+$/, ''),
        created: new Date().toISOString(),
        components: []
      }, null, 2);
    }
    
    // Schrijf bestand
    await fs.writeFile(filePath, fileContent, 'utf-8');
    
    // Valideer nieuw bestand
    const validation = await fileSystemManager.validateFile(filePath);
    
    res.status(201).json({
      success: true,
      message: 'Bestand aangemaakt',
      file: {
        name: fileName,
        path: filePath,
        validation
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Kon bestand niet aanmaken',
      details: error.message
    });
  }
};
