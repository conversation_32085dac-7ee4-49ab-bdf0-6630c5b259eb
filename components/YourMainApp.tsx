
import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { AnimationUtils } from '../utils/animations';
import { AnimatedButton } from './AnimatedButton';
import { FileBrowser } from './FileBrowser';

interface YourMainAppProps {
  serverUrl: string | null;
}

export const YourMainApp: React.FC<YourMainAppProps> = ({ serverUrl }) => {
  const [activeTab, setActiveTab] = useState<'home' | 'files'>('home');
  const { theme, isDark, toggleTheme } = useTheme();
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const screenWidth = Dimensions.get('window').width;

  // Animate tab transitions
  useEffect(() => {
    AnimationUtils.configureLayoutAnimation(theme.animation.normal);

    // Fade and slide animation for tab content
    Animated.parallel([
      AnimationUtils.fadeOut(fadeAnim, theme.animation.fast),
      Animated.timing(slideAnim, {
        toValue: activeTab === 'home' ? 0 : -screenWidth * 0.1,
        duration: theme.animation.normal,
        useNativeDriver: true,
      }),
    ]).start(() => {
      AnimationUtils.fadeIn(fadeAnim, theme.animation.fast).start();
    });
  }, [activeTab]);

  const handleApiTest = async () => {
    if (!serverUrl) {
      Alert.alert('Fout', 'Geen server URL beschikbaar');
      return;
    }

    try {
      // Test API call naar de server
      const response = await fetch(`${serverUrl}/health`);
      const data = await response.json();
      
      Alert.alert('API Test', `Server status: ${data.status || 'OK'}`);
    } catch (error) {
      Alert.alert('API Fout', `Kon geen verbinding maken: ${error.message}`);
    }
  };

  const handleDiagnostics = async () => {
    if (!serverUrl) {
      Alert.alert('Fout', 'Geen server URL beschikbaar');
      return;
    }

    try {
      const response = await fetch(`${serverUrl}/api/diagnostics`);
      const data = await response.json();
      
      Alert.alert('Diagnostics', JSON.stringify(data, null, 2));
    } catch (error) {
      Alert.alert('Diagnostics Fout', `Kon diagnostics niet ophalen: ${error.message}`);
    }
  };

  const renderHomeTab = () => (
    <Animated.View
      style={{
        flex: 1,
        opacity: fadeAnim,
        transform: [{ translateX: slideAnim }],
      }}
    >
      <ScrollView contentContainerStyle={[styles.content, { backgroundColor: theme.colors.background }]}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Hoofdapplicatie</Text>

        {/* Theme Toggle Button */}
        <AnimatedButton
          title={isDark ? 'Light' : 'Dark'}
          onPress={toggleTheme}
          variant="secondary"
          size="small"
        />

        <View style={[styles.infoContainer, { backgroundColor: theme.colors.surface, ...theme.shadows.small }]}>
          <Text style={[styles.infoLabel, { color: theme.colors.textSecondary }]}>Verbonden met server:</Text>
          <Text style={[styles.infoValue, { color: theme.colors.text }]}>{serverUrl || 'Geen URL'}</Text>
        </View>

        <View style={styles.actionContainer}>
          <AnimatedButton
            title="Test API Verbinding"
            onPress={handleApiTest}
            variant="primary"
            icon={<Text style={{ color: 'white' }}>🔗</Text>}
          />

          <AnimatedButton
            title="Toon Diagnostics"
            onPress={handleDiagnostics}
            variant="info"
            icon={<Text style={{ color: 'white' }}>📊</Text>}
          />

          <AnimatedButton
            title="📁 Open Bestandsbrowser"
            onPress={() => setActiveTab('files')}
            variant="success"
          />
        </View>

        <View style={[styles.placeholderContainer, { backgroundColor: theme.colors.surface, ...theme.shadows.small }]}>
          <Text style={[styles.placeholderTitle, { color: theme.colors.text }]}>🚀 Je App Functionaliteit</Text>
          <Text style={[styles.placeholderText, { color: theme.colors.textSecondary }]}>
            Je hebt nu een complete bestandsbrowser geïntegreerd met je FileSystemManager!
          </Text>

          <View style={[styles.featureList, { backgroundColor: theme.colors.background }]}>
            <Text style={[styles.featureItem, { color: theme.colors.textSecondary }]}>• API integratie is geconfigureerd</Text>
            <Text style={[styles.featureItem, { color: theme.colors.textSecondary }]}>• Server verbinding is actief</Text>
            <Text style={[styles.featureItem, { color: theme.colors.textSecondary }]}>• Bestandsbrowser beschikbaar</Text>
            <Text style={[styles.featureItem, { color: theme.colors.textSecondary }]}>• Figma integratie ondersteund</Text>
            <Text style={[styles.featureItem, { color: theme.colors.textSecondary }]}>• Automatische bestandsvalidatie</Text>
            <Text style={[styles.featureItem, { color: theme.colors.success }]}>• ✨ Dark Mode & Animaties!</Text>
          </View>
        </View>
      </ScrollView>
    </Animated.View>
  );

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Tab Navigation */}
      <View style={[styles.tabContainer, { backgroundColor: theme.colors.tabBackground, borderBottomColor: theme.colors.border }]}>
        <TouchableOpacity
          style={[
            styles.tab,
            { backgroundColor: activeTab === 'home' ? theme.colors.surface : 'transparent' },
            activeTab === 'home' && { borderBottomColor: theme.colors.tabActive }
          ]}
          onPress={() => setActiveTab('home')}
        >
          <Text style={[
            styles.tabText,
            { color: activeTab === 'home' ? theme.colors.tabActive : theme.colors.tabInactive }
          ]}>
            🏠 Home
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.tab,
            { backgroundColor: activeTab === 'files' ? theme.colors.surface : 'transparent' },
            activeTab === 'files' && { borderBottomColor: theme.colors.tabActive }
          ]}
          onPress={() => setActiveTab('files')}
        >
          <Text style={[
            styles.tabText,
            { color: activeTab === 'files' ? theme.colors.tabActive : theme.colors.tabInactive }
          ]}>
            📁 Bestanden
          </Text>
        </TouchableOpacity>
      </View>

      {/* Tab Content */}
      {activeTab === 'home' ? renderHomeTab() : (
        serverUrl ? (
          <FileBrowser serverUrl={serverUrl} />
        ) : (
          <View style={[styles.errorContainer, { backgroundColor: theme.colors.background }]}>
            <Text style={[styles.errorText, { color: theme.colors.error }]}>Geen server verbinding beschikbaar</Text>
            <AnimatedButton
              title="Terug naar Home"
              onPress={() => setActiveTab('home')}
              variant="primary"
            />
          </View>
        )
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
  },
  infoContainer: {
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  infoLabel: {
    fontSize: 14,
    marginBottom: 5,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
    fontFamily: 'monospace',
  },
  actionContainer: {
    gap: 15,
    marginBottom: 30,
  },
  placeholderContainer: {
    padding: 20,
    borderRadius: 10,
  },
  placeholderTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  placeholderText: {
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 20,
    textAlign: 'center',
  },
  featureList: {
    padding: 15,
    borderRadius: 8,
  },
  featureItem: {
    fontSize: 14,
    marginBottom: 8,
    lineHeight: 20,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
  },
  tab: {
    flex: 1,
    paddingVertical: 15,
    paddingHorizontal: 10,
    alignItems: 'center',
    borderBottomWidth: 3,
    borderBottomColor: 'transparent',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 20,
  },
});
