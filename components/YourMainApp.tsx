import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert
} from 'react-native';
import { FileBrowser } from './FileBrowser';

interface YourMainAppProps {
  serverUrl: string | null;
}

export const YourMainApp: React.FC<YourMainAppProps> = ({ serverUrl }) => {
  const [activeTab, setActiveTab] = useState<'home' | 'files'>('home');

  const handleApiTest = async () => {
    if (!serverUrl) {
      Alert.alert('Fout', 'Geen server URL beschikbaar');
      return;
    }

    try {
      // Test API call naar de server
      const response = await fetch(`${serverUrl}/health`);
      const data = await response.json();
      
      Alert.alert('API Test', `Server status: ${data.status || 'OK'}`);
    } catch (error) {
      Alert.alert('API Fout', `Kon geen verbinding maken: ${error.message}`);
    }
  };

  const handleDiagnostics = async () => {
    if (!serverUrl) {
      Alert.alert('Fout', 'Geen server URL beschikbaar');
      return;
    }

    try {
      const response = await fetch(`${serverUrl}/api/diagnostics`);
      const data = await response.json();
      
      Alert.alert('Diagnostics', JSON.stringify(data, null, 2));
    } catch (error) {
      Alert.alert('Diagnostics Fout', `Kon diagnostics niet ophalen: ${error.message}`);
    }
  };

  const renderHomeTab = () => (
    <ScrollView contentContainerStyle={styles.content}>
      <Text style={styles.title}>Hoofdapplicatie</Text>
      
      <View style={styles.infoContainer}>
        <Text style={styles.infoLabel}>Verbonden met server:</Text>
        <Text style={styles.infoValue}>{serverUrl || 'Geen URL'}</Text>
      </View>

      <View style={styles.actionContainer}>
        <TouchableOpacity style={styles.button} onPress={handleApiTest}>
          <Text style={styles.buttonText}>Test API Verbinding</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.button} onPress={handleDiagnostics}>
          <Text style={styles.buttonText}>Toon Diagnostics</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.button, styles.fileBrowserButton]} 
          onPress={() => setActiveTab('files')}
        >
          <Text style={styles.buttonText}>📁 Open Bestandsbrowser</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.placeholderContainer}>
        <Text style={styles.placeholderTitle}>🚀 Je App Functionaliteit</Text>
        <Text style={styles.placeholderText}>
          Je hebt nu een complete bestandsbrowser geïntegreerd met je FileSystemManager!
        </Text>
        
        <View style={styles.featureList}>
          <Text style={styles.featureItem}>• API integratie is geconfigureerd</Text>
          <Text style={styles.featureItem}>• Server verbinding is actief</Text>
          <Text style={styles.featureItem}>• Bestandsbrowser beschikbaar</Text>
          <Text style={styles.featureItem}>• Figma integratie ondersteund</Text>
          <Text style={styles.featureItem}>• Automatische bestandsvalidatie</Text>
        </View>
      </View>
    </ScrollView>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'home' && styles.activeTab]}
          onPress={() => setActiveTab('home')}
        >
          <Text style={[styles.tabText, activeTab === 'home' && styles.activeTabText]}>
            🏠 Home
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.tab, activeTab === 'files' && styles.activeTab]}
          onPress={() => setActiveTab('files')}
        >
          <Text style={[styles.tabText, activeTab === 'files' && styles.activeTabText]}>
            📁 Bestanden
          </Text>
        </TouchableOpacity>
      </View>

      {/* Tab Content */}
      {activeTab === 'home' ? renderHomeTab() : (
        serverUrl ? (
          <FileBrowser serverUrl={serverUrl} />
        ) : (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Geen server verbinding beschikbaar</Text>
            <TouchableOpacity 
              style={styles.button} 
              onPress={() => setActiveTab('home')}
            >
              <Text style={styles.buttonText}>Terug naar Home</Text>
            </TouchableOpacity>
          </View>
        )
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  infoContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  infoValue: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
    fontFamily: 'monospace',
  },
  actionContainer: {
    gap: 15,
    marginBottom: 30,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  placeholderContainer: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  placeholderTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
    textAlign: 'center',
  },
  placeholderText: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    marginBottom: 20,
    textAlign: 'center',
  },
  featureList: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 8,
  },
  featureItem: {
    fontSize: 14,
    color: '#495057',
    marginBottom: 8,
    lineHeight: 20,
  },
  fileBrowserButton: {
    backgroundColor: '#28a745',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flex: 1,
    paddingVertical: 15,
    paddingHorizontal: 10,
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 3,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    backgroundColor: 'white',
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
  },
  activeTabText: {
    color: '#007AFF',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#dc3545',
    textAlign: 'center',
    marginBottom: 20,
  },
});
