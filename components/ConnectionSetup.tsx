import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  StyleSheet,
  SafeAreaView
} from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { AnimatedButton } from './AnimatedButton';
import { ConnectionManager, ConnectionStatus } from '../services/ConnectionManager';

interface ConnectionSetupProps {
  onConnectionEstablished: (baseUrl: string) => void;
}

export const ConnectionSetup: React.FC<ConnectionSetupProps> = ({
  onConnectionEstablished
}) => {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>({
    isConnected: false,
    lastChecked: new Date()
  });
  const [manualIp, setManualIp] = useState('');
  const [isConnecting, setIsConnecting] = useState(false);
  const [showManualInput, setShowManualInput] = useState(false);
  const { theme } = useTheme();

  const connectionManager = ConnectionManager.getInstance();

  useEffect(() => {
    // Luister naar status updates
    const handleStatusUpdate = (status: ConnectionStatus) => {
      setConnectionStatus(status);
      setIsConnecting(false);
      
      if (status.isConnected && status.config) {
        onConnectionEstablished(status.config.baseUrl);
      }
    };

    connectionManager.addStatusListener(handleStatusUpdate);

    // Probeer automatisch te verbinden bij opstarten
    handleAutoConnect();

    return () => {
      connectionManager.removeStatusListener(handleStatusUpdate);
    };
  }, []);

  const handleAutoConnect = async () => {
    setIsConnecting(true);
    try {
      await connectionManager.connect();
    } catch (error) {
      console.error('Auto connect failed:', error);
    }
  };

  const handleManualConnect = async () => {
    if (!manualIp.trim()) {
      Alert.alert('Fout', 'Voer een geldig IP-adres in');
      return;
    }

    setIsConnecting(true);
    try {
      await connectionManager.setManualConfiguration(manualIp.trim());
    } catch (error) {
      Alert.alert('Verbindingsfout', error.message);
    }
  };

  const handleRetry = () => {
    handleAutoConnect();
  };

  const getStatusColor = () => {
    if (isConnecting) return '#FFA500';
    return connectionStatus.isConnected ? '#4CAF50' : '#F44336';
  };

  const getStatusText = () => {
    if (isConnecting) return 'Verbinding maken...';
    if (connectionStatus.isConnected) {
      return `Verbonden met ${connectionStatus.config?.baseUrl}`;
    }
    return connectionStatus.error || 'Niet verbonden';
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: theme.colors.text }]}>Ontwikkelserver Verbinding</Text>

        {/* Status Indicator */}
        <View style={[styles.statusContainer, { backgroundColor: getStatusColor() }]}>
          {isConnecting && <ActivityIndicator color="white" style={styles.spinner} />}
          <Text style={styles.statusText}>{getStatusText()}</Text>
        </View>

        {/* Connection Info */}
        {connectionStatus.config && (
          <View style={[styles.infoContainer, { backgroundColor: theme.colors.surface, ...theme.shadows.small }]}>
            <Text style={[styles.infoLabel, { color: theme.colors.textSecondary }]}>Server URL:</Text>
            <Text style={[styles.infoValue, { color: theme.colors.text }]}>{connectionStatus.config.baseUrl}</Text>

            {connectionStatus.config.detectedIp && (
              <>
                <Text style={[styles.infoLabel, { color: theme.colors.textSecondary }]}>Gedetecteerd IP:</Text>
                <Text style={[styles.infoValue, { color: theme.colors.text }]}>{connectionStatus.config.detectedIp}</Text>
              </>
            )}

            <Text style={[styles.infoLabel, { color: theme.colors.textSecondary }]}>Netwerk Type:</Text>
            <Text style={[styles.infoValue, { color: theme.colors.text }]}>{connectionStatus.config.networkType || 'Onbekend'}</Text>
          </View>
        )}

        {/* Action Buttons */}
        {!connectionStatus.isConnected && (
          <View style={styles.actionContainer}>
            <AnimatedButton
              title={isConnecting ? 'Bezig...' : 'Opnieuw Proberen'}
              onPress={handleRetry}
              disabled={isConnecting}
              loading={isConnecting}
              variant="primary"
              icon={<Text style={{ color: 'white' }}>🔄</Text>}
            />

            <AnimatedButton
              title="Handmatig Configureren"
              onPress={() => setShowManualInput(!showManualInput)}
              variant="secondary"
              icon={<Text style={{ color: theme.colors.primary }}>⚙️</Text>}
            />
          </View>
        )}

        {/* Manual Configuration */}
        {showManualInput && (
          <View style={[styles.manualContainer, { backgroundColor: theme.colors.surface, ...theme.shadows.small }]}>
            <Text style={[styles.manualTitle, { color: theme.colors.text }]}>Handmatige Configuratie</Text>
            <Text style={[styles.manualHint, { color: theme.colors.textSecondary }]}>
              Voer het IP-adres van je ontwikkelmachine in:
            </Text>

            <TextInput
              style={[
                styles.ipInput,
                {
                  borderColor: theme.colors.border,
                  backgroundColor: theme.colors.background,
                  color: theme.colors.text,
                }
              ]}
              value={manualIp}
              onChangeText={setManualIp}
              placeholder="bijv. *************"
              placeholderTextColor={theme.colors.textMuted}
              keyboardType="numeric"
              autoCapitalize="none"
              autoCorrect={false}
            />

            <AnimatedButton
              title="Verbinden"
              onPress={handleManualConnect}
              disabled={isConnecting}
              loading={isConnecting}
              variant="success"
              icon={<Text style={{ color: 'white' }}>🔗</Text>}
            />

            <Text style={[styles.helpText, { color: theme.colors.textMuted }]}>
              💡 Tip: Vind je IP-adres door in de terminal 'ipconfig' (Windows) of 'ifconfig' (Mac/Linux) uit te voeren
            </Text>
          </View>
        )}

        {/* Success Actions */}
        {connectionStatus.isConnected && (
          <AnimatedButton
            title="Doorgaan naar App"
            onPress={() => onConnectionEstablished(connectionStatus.config!.baseUrl)}
            variant="success"
            icon={<Text style={{ color: 'white' }}>🚀</Text>}
          />
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  spinner: {
    marginRight: 10,
  },
  statusText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  infoContainer: {
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  infoLabel: {
    fontSize: 14,
    marginTop: 10,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '500',
  },
  actionContainer: {
    gap: 10,
  },
  manualContainer: {
    padding: 20,
    borderRadius: 10,
    marginTop: 20,
  },
  manualTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  manualHint: {
    fontSize: 14,
    marginBottom: 15,
  },
  ipInput: {
    borderWidth: 1,
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
    marginBottom: 15,
  },
  helpText: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 18,
    marginTop: 15,
  },
});
