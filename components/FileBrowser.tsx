import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  Modal,
  TextInput,
  ScrollView,
  ActivityIndicator
} from 'react-native';

interface Project {
  id: string;
  name: string;
  status: 'valid' | 'corrupt' | 'inaccessible' | 'incompatible';
  fileCount: number;
  lastModified: string | null;
  figmaStatus?: {
    connected: boolean;
    hasChanges?: boolean;
  };
}

interface ProjectFile {
  name: string;
  path: string;
  status: 'valid' | 'corrupt' | 'inaccessible' | 'incompatible' | 'missing_figma';
  size?: number;
  lastModified?: string;
  version?: string;
  figmaFileId?: string;
  error?: string;
}

interface FileBrowserProps {
  serverUrl: string;
}

export const FileBrowser: React.FC<FileBrowserProps> = ({ serverUrl }) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [projectFiles, setProjectFiles] = useState<ProjectFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newFileName, setNewFileName] = useState('');
  const [fileContent, setFileContent] = useState('');
  const [showFileModal, setShowFileModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState<ProjectFile | null>(null);

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${serverUrl}/api/files`);
      const data = await response.json();
      
      if (data.success) {
        setProjects(data.projects);
      } else {
        Alert.alert('Fout', data.error || 'Kon projecten niet laden');
      }
    } catch (error) {
      Alert.alert('Netwerkfout', `Kon projecten niet laden: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const loadProjectFiles = async (project: Project) => {
    setLoading(true);
    try {
      const response = await fetch(`${serverUrl}/api/files/${project.id}`);
      const data = await response.json();
      
      if (data.success) {
        setProjectFiles(data.files);
        setSelectedProject(project);
      } else {
        Alert.alert('Fout', data.error || 'Kon bestanden niet laden');
      }
    } catch (error) {
      Alert.alert('Netwerkfout', `Kon bestanden niet laden: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const loadFileContent = async (file: ProjectFile) => {
    if (!selectedProject) return;
    
    setLoading(true);
    try {
      const response = await fetch(`${serverUrl}/api/files/${selectedProject.id}/${file.name}`);
      const data = await response.json();
      
      if (data.success) {
        setFileContent(data.file.content);
        setSelectedFile(file);
        setShowFileModal(true);
      } else {
        Alert.alert('Fout', data.error || 'Kon bestand niet laden');
      }
    } catch (error) {
      Alert.alert('Netwerkfout', `Kon bestand niet laden: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const createFile = async () => {
    if (!selectedProject || !newFileName.trim()) {
      Alert.alert('Fout', 'Voer een geldige bestandsnaam in');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`${serverUrl}/api/files/${selectedProject.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileName: newFileName,
          type: 'json'
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        Alert.alert('Succes', 'Bestand aangemaakt');
        setNewFileName('');
        setShowCreateModal(false);
        loadProjectFiles(selectedProject);
      } else {
        Alert.alert('Fout', data.error || 'Kon bestand niet aanmaken');
      }
    } catch (error) {
      Alert.alert('Netwerkfout', `Kon bestand niet aanmaken: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const recoverFile = async (file: ProjectFile) => {
    if (!selectedProject) return;

    Alert.alert(
      'Bestand herstellen',
      `Weet je zeker dat je ${file.name} wilt proberen te herstellen?`,
      [
        { text: 'Annuleren', style: 'cancel' },
        {
          text: 'Herstellen',
          onPress: async () => {
            setLoading(true);
            try {
              const response = await fetch(
                `${serverUrl}/api/files/${selectedProject.id}/${file.name}/recover`,
                { method: 'POST' }
              );
              const data = await response.json();
              
              if (data.success) {
                Alert.alert('Succes', `Bestand hersteld via ${data.method}`);
                loadProjectFiles(selectedProject);
              } else {
                Alert.alert('Fout', data.error || 'Herstel gefaald');
              }
            } catch (error) {
              Alert.alert('Netwerkfout', `Herstel gefaald: ${error.message}`);
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const deleteFile = async (file: ProjectFile) => {
    if (!selectedProject) return;

    Alert.alert(
      'Bestand verwijderen',
      `Weet je zeker dat je ${file.name} wilt verwijderen? Er wordt een backup gemaakt.`,
      [
        { text: 'Annuleren', style: 'cancel' },
        {
          text: 'Verwijderen',
          style: 'destructive',
          onPress: async () => {
            setLoading(true);
            try {
              const response = await fetch(
                `${serverUrl}/api/files/${selectedProject.id}/${file.name}`,
                { method: 'DELETE' }
              );
              const data = await response.json();
              
              if (data.success) {
                Alert.alert('Succes', 'Bestand verwijderd (backup gemaakt)');
                loadProjectFiles(selectedProject);
              } else {
                Alert.alert('Fout', data.error || 'Verwijderen gefaald');
              }
            } catch (error) {
              Alert.alert('Netwerkfout', `Verwijderen gefaald: ${error.message}`);
            } finally {
              setLoading(false);
            }
          }
        }
      ]
    );
  };

  const onRefresh = async () => {
    setRefreshing(true);
    if (selectedProject) {
      await loadProjectFiles(selectedProject);
    } else {
      await loadProjects();
    }
    setRefreshing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'valid': return '#28a745';
      case 'corrupt': return '#dc3545';
      case 'inaccessible': return '#6c757d';
      case 'incompatible': return '#fd7e14';
      case 'missing_figma': return '#ffc107';
      default: return '#6c757d';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'valid': return 'Geldig';
      case 'corrupt': return 'Corrupt';
      case 'inaccessible': return 'Niet toegankelijk';
      case 'incompatible': return 'Incompatibel';
      case 'missing_figma': return 'Figma ontbreekt';
      default: return 'Onbekend';
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Onbekend';
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Onbekend';
    return new Date(dateString).toLocaleString('nl-NL');
  };

  const renderProject = ({ item }: { item: Project }) => (
    <TouchableOpacity
      style={[styles.projectItem, { borderLeftColor: getStatusColor(item.status) }]}
      onPress={() => loadProjectFiles(item)}
    >
      <View style={styles.projectHeader}>
        <Text style={styles.projectName}>{item.name}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
        </View>
      </View>
      
      <View style={styles.projectDetails}>
        <Text style={styles.projectDetail}>{item.fileCount} bestanden</Text>
        {item.lastModified && (
          <Text style={styles.projectDetail}>
            Laatst gewijzigd: {formatDate(item.lastModified)}
          </Text>
        )}
        {item.figmaStatus?.connected && (
          <Text style={[styles.projectDetail, { color: '#007AFF' }]}>
            📎 Figma gekoppeld {item.figmaStatus.hasChanges ? '(wijzigingen)' : ''}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderFile = ({ item }: { item: ProjectFile }) => (
    <TouchableOpacity
      style={[styles.fileItem, { borderLeftColor: getStatusColor(item.status) }]}
      onPress={() => loadFileContent(item)}
      onLongPress={() => {
        Alert.alert(
          item.name,
          'Wat wil je doen?',
          [
            { text: 'Annuleren', style: 'cancel' },
            { text: 'Bekijken', onPress: () => loadFileContent(item) },
            ...(item.status === 'corrupt' ? [
              { text: 'Herstellen', onPress: () => recoverFile(item) }
            ] : []),
            { text: 'Verwijderen', style: 'destructive', onPress: () => deleteFile(item) }
          ]
        );
      }}
    >
      <View style={styles.fileHeader}>
        <Text style={styles.fileName}>{item.name}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
        </View>
      </View>
      
      <View style={styles.fileDetails}>
        <Text style={styles.fileDetail}>Grootte: {formatFileSize(item.size)}</Text>
        {item.lastModified && (
          <Text style={styles.fileDetail}>
            Gewijzigd: {formatDate(item.lastModified)}
          </Text>
        )}
        {item.version && (
          <Text style={styles.fileDetail}>Versie: {item.version}</Text>
        )}
        {item.figmaFileId && (
          <Text style={[styles.fileDetail, { color: '#007AFF' }]}>
            📎 Figma: {item.figmaFileId.substring(0, 8)}...
          </Text>
        )}
        {item.error && (
          <Text style={[styles.fileDetail, { color: '#dc3545' }]}>
            ⚠️ {item.error}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );

  if (loading && !refreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Laden...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.headerButton, !selectedProject && styles.headerButtonDisabled]}
          onPress={() => {
            setSelectedProject(null);
            setProjectFiles([]);
          }}
          disabled={!selectedProject}
        >
          <Text style={styles.headerButtonText}>← Projecten</Text>
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>
          {selectedProject ? selectedProject.name : 'Bestandsbrowser'}
        </Text>
        
        {selectedProject && (
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowCreateModal(true)}
          >
            <Text style={styles.headerButtonText}>+ Nieuw</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Content */}
      <FlatList
        data={selectedProject ? projectFiles : projects}
        renderItem={selectedProject ? renderFile : renderProject}
        keyExtractor={(item) => selectedProject ? (item as ProjectFile).name : (item as Project).id}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        style={styles.list}
        showsVerticalScrollIndicator={false}
      />

      {/* Create File Modal */}
      <Modal
        visible={showCreateModal}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setShowCreateModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Nieuw bestand</Text>
            
            <TextInput
              style={styles.textInput}
              placeholder="Bestandsnaam (bijv. project.json)"
              value={newFileName}
              onChangeText={setNewFileName}
              autoCapitalize="none"
            />
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setShowCreateModal(false);
                  setNewFileName('');
                }}
              >
                <Text style={styles.cancelButtonText}>Annuleren</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, styles.createButton]}
                onPress={createFile}
              >
                <Text style={styles.createButtonText}>Aanmaken</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* File Content Modal */}
      <Modal
        visible={showFileModal}
        animationType="slide"
        onRequestClose={() => setShowFileModal(false)}
      >
        <View style={styles.fileModalContainer}>
          <View style={styles.fileModalHeader}>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowFileModal(false)}
            >
              <Text style={styles.closeButtonText}>✕ Sluiten</Text>
            </TouchableOpacity>
            <Text style={styles.fileModalTitle}>
              {selectedFile?.name}
            </Text>
          </View>
          
          <ScrollView style={styles.fileContentContainer}>
            <Text style={styles.fileContentText}>{fileContent}</Text>
          </ScrollView>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#007AFF',
    borderRadius: 6,
    minWidth: 80,
    alignItems: 'center',
  },
  headerButtonDisabled: {
    backgroundColor: '#ccc',
  },
  headerButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textAlign: 'center',
  },
  list: {
    flex: 1,
  },
  projectItem: {
    backgroundColor: 'white',
    marginHorizontal: 15,
    marginVertical: 5,
    padding: 15,
    borderRadius: 10,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  projectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  projectName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  projectDetails: {
    gap: 4,
  },
  projectDetail: {
    fontSize: 14,
    color: '#666',
  },
  fileItem: {
    backgroundColor: 'white',
    marginHorizontal: 15,
    marginVertical: 5,
    padding: 15,
    borderRadius: 10,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  fileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    fontFamily: 'monospace',
  },
  fileDetails: {
    gap: 4,
  },
  fileDetail: {
    fontSize: 13,
    color: '#666',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    margin: 20,
    padding: 20,
    borderRadius: 10,
    width: '80%',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
    color: '#333',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
  },
  modalButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: '#6c757d',
  },
  cancelButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  createButton: {
    backgroundColor: '#28a745',
  },
  createButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  fileModalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  fileModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    backgroundColor: '#f8f9fa',
  },
  closeButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#6c757d',
    borderRadius: 6,
    marginRight: 15,
  },
  closeButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  fileModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  fileContentContainer: {
    flex: 1,
    padding: 15,
  },
  fileContentText: {
    fontSize: 14,
    color: '#333',
    fontFamily: 'monospace',
    lineHeight: 20,
  },
});
