
import React, { useState, useEffect } from 'react';
import { StatusBar } from 'react-native';
import { ThemeProvider, useTheme } from './contexts/ThemeContext';
import { ConnectionSetup } from './components/ConnectionSetup';
import { YourMainApp } from './components/YourMainApp';

const AppContent: React.FC = () => {
  const [serverUrl, setServerUrl] = useState<string | null>(null);
  const [isReady, setIsReady] = useState(false);
  const { theme, isDark } = useTheme();

  const handleConnectionEstablished = (baseUrl: string) => {
    setServerUrl(baseUrl);
    setIsReady(true);

    // Configureer je API client met de juiste base URL
    configureApiClient(baseUrl);
  };

  const configureApiClient = (baseUrl: string) => {
    // Hier configureer je je API client (axios, fetch, etc.)
    // Bijvoorbeeld:
    // axios.defaults.baseURL = baseUrl;
    console.log('API configured with base URL:', baseUrl);
  };

  return (
    <>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={theme.colors.background}
      />
      {!isReady ? (
        <ConnectionSetup onConnectionEstablished={handleConnectionEstablished} />
      ) : (
        <YourMainApp serverUrl={serverUrl} />
      )}
    </>
  );
};

export default function App() {
  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  );
}
