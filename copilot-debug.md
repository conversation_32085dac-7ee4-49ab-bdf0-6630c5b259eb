# GitHub Copilot Debug Guide

## Check These Steps:

### 1. Open VS Code Command Palette

- Press `Ctrl+Shift+P` (Windows/Linux) or `Cmd+Shift+P` (Mac)

### 2. Run Copilot Diagnostics

Try these commands one by one:

```
GitHub Copilot: Check Status
GitHub Copilot: Show Output Channel
GitHub Copilot Chat: Show Output Channel
```

### 3. Check Extension Status

- Go to Extensions (Ctrl+Shift+X)
- Look for:
  - ✅ GitHub Copilot (should be enabled)
  - ✅ GitHub Copilot Chat (should be enabled)

### 4. Authentication Commands

```
GitHub Copilot: Sign Out
GitHub Copilot: Sign In
GitHub Copilot Chat: Sign In
```

### 5. Check Settings

Open Settings (Ctrl+,) and search for "copilot":

- ✅ `github.copilot.enable` should be true
- ✅ `github.copilot.chat.enabled` should be true

### 6. Common Error Messages and Solutions

**"Not signed in"**

- Run: `GitHub Copilot: Sign In`

**"Subscription required"**

- Go to: https://github.com/settings/copilot
- Activate subscription or apply for student access

**"Network error"**

- Check internet connection
- Try: `GitHub Copilot: Reset Auth`

**"Extension not working"**

- Reload window: `Developer: Reload Window`
- Or restart VS Code completely

### 7. Output Logs

Check the Output panel:

- View → Output
- Select "GitHub Copilot" from dropdown
- Look for error messages

### 8. Last Resort

If nothing works:

1. Uninstall both Copilot extensions
2. Restart VS Code
3. Reinstall extensions
4. Sign in again
