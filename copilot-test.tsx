import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';

// Test Copilot by typing comments and letting it suggest code
// Try typing: "// Create a function that calculates fibonacci numbers"

// TODO: Let Copilot help you create a React component
// Type: "const CopilotTestComponent = () => {" and see what it suggests

// Test 1: Let Copilot create a simple counter component
// Type the comment below and press Tab when you see suggestions
// Create a counter component with increment and decrement buttons

// Test 2: Let Copilot help with TypeScript interfaces
// Type: "interface User {" and see the suggestions

// Test 3: Let Copilot help with React hooks
// Type: "const [" and see what state variables it suggests

// Test 4: Let Copilot help with styling
// Type: "const styles = StyleSheet.create({" and see the suggestions

export const CopilotTest = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Copilot Test</Text>
      <Text style={styles.subtitle}>
        Try typing comments and let <PERSON><PERSON><PERSON> suggest code!
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
  },
});
