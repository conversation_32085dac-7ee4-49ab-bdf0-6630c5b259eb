import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { ThemeProvider, useTheme } from './contexts/ThemeContext';
import { AnimatedButton } from './components/AnimatedButton';

const DemoContent: React.FC = () => {
  const { theme, isDark, toggleTheme } = useTheme();

  return (
    <ScrollView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.content}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          🎨 Dark Mode & Animaties Demo
        </Text>
        
        <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
          Huidige thema: {isDark ? '🌙 Dark Mode' : '☀️ Light Mode'}
        </Text>

        {/* Theme Toggle */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface, ...theme.shadows.medium }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Theme Controls</Text>
          
          <AnimatedButton
            title={`Switch to ${isDark ? 'Light' : 'Dark'} Mode`}
            onPress={toggleTheme}
            variant="primary"
            icon={<Text style={{ color: 'white' }}>{isDark ? '☀️' : '🌙'}</Text>}
          />
        </View>

        {/* Button Variants */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface, ...theme.shadows.medium }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Button Variants</Text>
          
          <View style={styles.buttonGrid}>
            <AnimatedButton
              title="Primary"
              onPress={() => console.log('Primary pressed')}
              variant="primary"
              size="medium"
            />
            
            <AnimatedButton
              title="Secondary"
              onPress={() => console.log('Secondary pressed')}
              variant="secondary"
              size="medium"
            />
            
            <AnimatedButton
              title="Success"
              onPress={() => console.log('Success pressed')}
              variant="success"
              size="medium"
            />
            
            <AnimatedButton
              title="Warning"
              onPress={() => console.log('Warning pressed')}
              variant="warning"
              size="medium"
            />
            
            <AnimatedButton
              title="Error"
              onPress={() => console.log('Error pressed')}
              variant="error"
              size="medium"
            />
            
            <AnimatedButton
              title="Info"
              onPress={() => console.log('Info pressed')}
              variant="info"
              size="medium"
            />
          </View>
        </View>

        {/* Button Sizes */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface, ...theme.shadows.medium }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Button Sizes</Text>
          
          <View style={styles.buttonColumn}>
            <AnimatedButton
              title="Small Button"
              onPress={() => console.log('Small pressed')}
              variant="primary"
              size="small"
            />
            
            <AnimatedButton
              title="Medium Button"
              onPress={() => console.log('Medium pressed')}
              variant="primary"
              size="medium"
            />
            
            <AnimatedButton
              title="Large Button"
              onPress={() => console.log('Large pressed')}
              variant="primary"
              size="large"
            />
          </View>
        </View>

        {/* Loading States */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface, ...theme.shadows.medium }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Loading & Disabled States</Text>
          
          <View style={styles.buttonColumn}>
            <AnimatedButton
              title="Loading Button"
              onPress={() => console.log('Loading pressed')}
              variant="primary"
              loading={true}
            />
            
            <AnimatedButton
              title="Disabled Button"
              onPress={() => console.log('Disabled pressed')}
              variant="primary"
              disabled={true}
            />
          </View>
        </View>

        {/* Color Showcase */}
        <View style={[styles.section, { backgroundColor: theme.colors.surface, ...theme.shadows.medium }]}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text }]}>Color Palette</Text>
          
          <View style={styles.colorGrid}>
            <View style={[styles.colorBox, { backgroundColor: theme.colors.primary }]}>
              <Text style={styles.colorLabel}>Primary</Text>
            </View>
            <View style={[styles.colorBox, { backgroundColor: theme.colors.success }]}>
              <Text style={styles.colorLabel}>Success</Text>
            </View>
            <View style={[styles.colorBox, { backgroundColor: theme.colors.warning }]}>
              <Text style={styles.colorLabel}>Warning</Text>
            </View>
            <View style={[styles.colorBox, { backgroundColor: theme.colors.error }]}>
              <Text style={styles.colorLabel}>Error</Text>
            </View>
            <View style={[styles.colorBox, { backgroundColor: theme.colors.info }]}>
              <Text style={styles.colorLabel}>Info</Text>
            </View>
          </View>
        </View>

        <Text style={[styles.footer, { color: theme.colors.textMuted }]}>
          ✨ Probeer de buttons uit om de animaties te zien!
        </Text>
      </View>
    </ScrollView>
  );
};

export const Demo: React.FC = () => {
  return (
    <ThemeProvider>
      <DemoContent />
    </ThemeProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
  },
  section: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  buttonGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  buttonColumn: {
    gap: 10,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  colorBox: {
    width: 60,
    height: 60,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  colorLabel: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  footer: {
    textAlign: 'center',
    marginTop: 30,
    fontSize: 14,
  },
});
