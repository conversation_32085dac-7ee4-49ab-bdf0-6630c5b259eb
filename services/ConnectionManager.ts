import { NetworkUtils, NetworkConfig } from '../utils/networkUtils';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ConnectionStatus {
  isConnected: boolean;
  config?: NetworkConfig;
  error?: string;
  lastChecked: Date;
}

export class ConnectionManager {
  private static instance: ConnectionManager;
  private networkUtils: NetworkUtils;
  private currentStatus: ConnectionStatus;
  private statusListeners: ((status: ConnectionStatus) => void)[] = [];

  private constructor() {
    this.networkUtils = NetworkUtils.getInstance();
    this.currentStatus = {
      isConnected: false,
      lastChecked: new Date()
    };
  }

  static getInstance(): ConnectionManager {
    if (!ConnectionManager.instance) {
      ConnectionManager.instance = new ConnectionManager();
    }
    return ConnectionManager.instance;
  }

  /**
   * Probeert verbinding te maken met de ontwikkelserver
   */
  async connect(port: number = 3000): Promise<ConnectionStatus> {
    try {
      this.updateStatus({
        isConnected: false,
        error: 'Verbinding maken...',
        lastChecked: new Date()
      });

      // Probeer automatische detectie
      let config: NetworkConfig;
      try {
        config = await this.networkUtils.detectDevelopmentServerUrl(port);
      } catch (autoError) {
        console.log('Automatische detectie gefaald:', autoError);
        
        // Probeer opgeslagen configuratie
        const savedConfig = await this.getSavedConfiguration();
        if (savedConfig) {
          const isReachable = await this.networkUtils.testConnection(savedConfig.baseUrl);
          if (isReachable) {
            config = savedConfig;
          } else {
            throw new Error('Opgeslagen server configuratie is niet meer bereikbaar');
          }
        } else {
          throw autoError;
        }
      }

      // Sla werkende configuratie op
      await this.saveConfiguration(config);

      const status: ConnectionStatus = {
        isConnected: true,
        config,
        lastChecked: new Date()
      };

      this.updateStatus(status);
      return status;

    } catch (error) {
      const errorStatus: ConnectionStatus = {
        isConnected: false,
        error: error.message,
        lastChecked: new Date()
      };

      this.updateStatus(errorStatus);
      return errorStatus;
    }
  }

  /**
   * Test huidige verbinding
   */
  async testCurrentConnection(): Promise<boolean> {
    const config = this.currentStatus.config;
    if (!config) return false;

    return await this.networkUtils.testConnection(config.baseUrl);
  }

  /**
   * Handmatige server configuratie
   */
  async setManualConfiguration(ipAddress: string, port: number = 3000): Promise<ConnectionStatus> {
    try {
      const baseUrl = `http://${ipAddress}:${port}`;
      const isReachable = await this.networkUtils.testConnection(baseUrl);

      if (!isReachable) {
        throw new Error(`Server niet bereikbaar op ${baseUrl}`);
      }

      const config: NetworkConfig = {
        baseUrl,
        isLocalhost: false,
        detectedIp: ipAddress
      };

      await this.saveConfiguration(config);

      const status: ConnectionStatus = {
        isConnected: true,
        config,
        lastChecked: new Date()
      };

      this.updateStatus(status);
      return status;

    } catch (error) {
      const errorStatus: ConnectionStatus = {
        isConnected: false,
        error: error.message,
        lastChecked: new Date()
      };

      this.updateStatus(errorStatus);
      return errorStatus;
    }
  }

  /**
   * Voeg status listener toe
   */
  addStatusListener(listener: (status: ConnectionStatus) => void): void {
    this.statusListeners.push(listener);
  }

  /**
   * Verwijder status listener
   */
  removeStatusListener(listener: (status: ConnectionStatus) => void): void {
    const index = this.statusListeners.indexOf(listener);
    if (index > -1) {
      this.statusListeners.splice(index, 1);
    }
  }

  getCurrentStatus(): ConnectionStatus {
    return this.currentStatus;
  }

  private updateStatus(status: ConnectionStatus): void {
    this.currentStatus = status;
    this.statusListeners.forEach(listener => listener(status));
  }

  private async saveConfiguration(config: NetworkConfig): Promise<void> {
    try {
      await AsyncStorage.setItem('dev_server_config', JSON.stringify(config));
    } catch (error) {
      console.error('Kon configuratie niet opslaan:', error);
    }
  }

  private async getSavedConfiguration(): Promise<NetworkConfig | null> {
    try {
      const saved = await AsyncStorage.getItem('dev_server_config');
      return saved ? JSON.parse(saved) : null;
    } catch (error) {
      console.error('Kon configuratie niet laden:', error);
      return null;
    }
  }
}
