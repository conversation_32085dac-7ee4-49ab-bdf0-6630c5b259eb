{"version": 3, "names": ["NativeModules", "TurboModuleRegistry", "shouldFallbackToLegacyNativeModule", "RCTAsyncStorage", "get"], "sources": ["RCTAsyncStorage.ts"], "sourcesContent": ["import { NativeModules, TurboModuleRegistry } from \"react-native\";\nimport { shouldFallbackToLegacyNativeModule } from \"./shouldFallbackToLegacyNativeModule\";\n\n// TurboModuleRegistry falls back to NativeModules so we don't have to try go\n// assign NativeModules' counterparts if TurboModuleRegistry would resolve\n// with undefined.\nlet RCTAsyncStorage = TurboModuleRegistry\n  ? TurboModuleRegistry.get(\"PlatformLocalStorage\") || // Support for external modules, like react-native-windows\n    TurboModuleRegistry.get(\"RNC_AsyncSQLiteDBStorage\") ||\n    TurboModuleRegistry.get(\"RNCAsyncStorage\")\n  : NativeModules[\"PlatformLocalStorage\"] || // Support for external modules, like react-native-windows\n    NativeModules[\"RNC_AsyncSQLiteDBStorage\"] ||\n    NativeModules[\"RNCAsyncStorage\"];\n\nif (!RCTAsyncStorage && shouldFallbackToLegacyNativeModule()) {\n  if (TurboModuleRegistry) {\n    RCTAsyncStorage =\n      TurboModuleRegistry.get(\"AsyncSQLiteDBStorage\") ||\n      TurboModuleRegistry.get(\"AsyncLocalStorage\");\n  } else {\n    RCTAsyncStorage =\n      NativeModules[\"AsyncSQLiteDBStorage\"] ||\n      NativeModules[\"AsyncLocalStorage\"];\n  }\n}\n\nexport default RCTAsyncStorage;\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,mBAAmB,QAAQ,cAAc;AACjE,SAASC,kCAAkC,QAAQ,sCAAsC;;AAEzF;AACA;AACA;AACA,IAAIC,eAAe,GAAGF,mBAAmB,GACrCA,mBAAmB,CAACG,GAAG,CAAC,sBAAsB,CAAC;AAAI;AACnDH,mBAAmB,CAACG,GAAG,CAAC,0BAA0B,CAAC,IACnDH,mBAAmB,CAACG,GAAG,CAAC,iBAAiB,CAAC,GAC1CJ,aAAa,CAAC,sBAAsB,CAAC;AAAI;AACzCA,aAAa,CAAC,0BAA0B,CAAC,IACzCA,aAAa,CAAC,iBAAiB,CAAC;AAEpC,IAAI,CAACG,eAAe,IAAID,kCAAkC,CAAC,CAAC,EAAE;EAC5D,IAAID,mBAAmB,EAAE;IACvBE,eAAe,GACbF,mBAAmB,CAACG,GAAG,CAAC,sBAAsB,CAAC,IAC/CH,mBAAmB,CAACG,GAAG,CAAC,mBAAmB,CAAC;EAChD,CAAC,MAAM;IACLD,eAAe,GACbH,aAAa,CAAC,sBAAsB,CAAC,IACrCA,aAAa,CAAC,mBAAmB,CAAC;EACtC;AACF;AAEA,eAAeG,eAAe"}