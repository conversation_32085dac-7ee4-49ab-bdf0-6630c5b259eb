{"version": 3, "names": ["AsyncStorage", "useAsyncStorage", "key", "getItem", "args", "setItem", "mergeItem", "removeItem"], "sources": ["hooks.ts"], "sourcesContent": ["import AsyncStorage from \"./AsyncStorage\";\nimport type { AsyncStorageHook } from \"./types\";\n\nexport function useAsyncStorage(key: string): AsyncStorageHook {\n  return {\n    getItem: (...args) => AsyncStorage.getItem(key, ...args),\n    setItem: (...args) => AsyncStorage.setItem(key, ...args),\n    mergeItem: (...args) => AsyncStorage.mergeItem(key, ...args),\n    removeItem: (...args) => AsyncStorage.removeItem(key, ...args),\n  };\n}\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,gBAAgB;AAGzC,OAAO,SAASC,eAAeA,CAACC,GAAW,EAAoB;EAC7D,OAAO;IACLC,OAAO,EAAEA,CAAC,GAAGC,IAAI,KAAKJ,YAAY,CAACG,OAAO,CAACD,GAAG,EAAE,GAAGE,IAAI,CAAC;IACxDC,OAAO,EAAEA,CAAC,GAAGD,IAAI,KAAKJ,YAAY,CAACK,OAAO,CAACH,GAAG,EAAE,GAAGE,IAAI,CAAC;IACxDE,SAAS,EAAEA,CAAC,GAAGF,IAAI,KAAKJ,YAAY,CAACM,SAAS,CAACJ,GAAG,EAAE,GAAGE,IAAI,CAAC;IAC5DG,UAAU,EAAEA,CAAC,GAAGH,IAAI,KAAKJ,YAAY,CAACO,UAAU,CAACL,GAAG,EAAE,GAAGE,IAAI;EAC/D,CAAC;AACH"}