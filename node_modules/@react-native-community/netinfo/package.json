{"name": "@react-native-community/netinfo", "version": "9.5.0", "description": "React Native Network Info API for iOS & Android", "react-native": "src/index.ts", "types": "lib/typescript/src/index.d.ts", "main": "lib/commonjs/index.js", "module": "lib/module/index.js", "files": ["/android", "!/android/build", "/ios", "/windows", "/macos", "/src", "/lib", "/*.podspec", "/jest"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> (Salakar) <<EMAIL>>"], "homepage": "https://github.com/react-native-netinfo/react-native-netinfo#readme", "license": "MIT", "scripts": {"start": "react-native start", "start:android": "cd example && yarn react-native run-android", "start:ios": "cd example && yarn react-native run-ios --project-path `pwd`/ios", "start:macos:packager": "node node_modules/react-native-macos/local-cli/cli.js start --use-react-native-macos", "start:web": "webpack-dev-server --config example/webpack.config.js", "start:windows:packager": "react-native start --use-react-native-windows", "start:windows": "react-native run-windows --arch x64 --logging --root example", "test": "yarn validate:eslint && yarn validate:typescript && yarn test:jest", "validate:eslint": "eslint \"src/**/*.{js,ts,tsx}\" \"example/**/*.{js,ts,tsx}\"", "validate:typescript": "tsc --project ./ --noEmit", "test:jest": "jest \"/src/\"", "build:e2e:macos": "scripts/run_macos_e2e.sh 'build'", "test:e2e:macos": "scripts/run_macos_e2e.sh 'test'", "ci:publish": "yarn semantic-release", "semantic-release": "semantic-release", "prepare": "bob build"}, "keywords": ["react-native", "react native", "netinfo", "networking", "network info"], "peerDependencies": {"react-native": ">=0.59"}, "dependencies": {}, "devDependencies": {"@babel/core": "^7.12.17", "@babel/runtime": "^7.12.18", "@react-native-community/bob": "^0.17.1", "@react-native-community/cli": "^6.0.0", "@react-native-community/cli-platform-android": "^6.0.0", "@react-native-community/cli-platform-ios": "^6.0.0", "@react-native-community/eslint-config": "^3.0.1", "@semantic-release/changelog": "^5.0.1", "@semantic-release/git": "9.0.0", "@types/jest": "^27.0.2", "@types/react-native": "^0.65.1", "@typescript-eslint/eslint-plugin": "^4.32.0", "@typescript-eslint/parser": "^4.32.0", "babel-loader": "^8.0.6", "eslint": "7.31.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "3.1.2", "html-webpack-plugin": "^3.2.0", "husky": "^2.3.0", "jest": "^27.2.4", "jest-expo": "^42.1.0", "jest-fetch-mock": "^3.0.3", "lint-staged": "^8.1.7", "metro": "^0.66.2", "metro-config": "^0.66.1", "metro-react-native-babel-preset": "^0.66.2", "prettier": "^1.17.0", "react": "17.0.2", "react-dom": "17.0.2", "react-native": "0.65.1", "react-native-codegen": "^0.0.8", "react-native-macos": "0.60.0-microsoft.50", "react-native-web": "^0.17.5", "react-native-windows": "0.65.8", "react-test-renderer": "17.0.2", "rimraf": "^2.6.3", "semantic-release": "^17.4.6", "source-map-loader": "^0.2.4", "ts-jest": "^27.0.5", "ts-loader": "^6.2.1", "typescript": "^4.4.3", "webpack": "^4.41.5", "webpack-cli": "^3.3.10", "webpack-dev-server": "^3.10.2"}, "repository": {"type": "git", "url": "https://github.com/react-native-netinfo/react-native-netinfo.git"}, "@react-native-community/bob": {"source": "src", "output": "lib", "targets": ["commonjs", "module", "typescript"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "yarn test"}}, "lint-staged": {"*.{js,ts,tsx}": ["yarn eslint --fix", "git add"]}}