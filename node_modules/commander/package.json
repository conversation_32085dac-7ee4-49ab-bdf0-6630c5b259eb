{"name": "commander", "version": "12.1.0", "description": "the complete solution for node.js command-line programs", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/tj/commander.js.git"}, "scripts": {"check": "npm run check:type && npm run check:lint && npm run check:format", "check:format": "prettier --check .", "check:lint": "eslint .", "check:type": "npm run check:type:js && npm run check:type:ts", "check:type:ts": "tsd && tsc -p tsconfig.ts.json", "check:type:js": "tsc -p tsconfig.js.json", "fix": "npm run fix:lint && npm run fix:format", "fix:format": "prettier --write .", "fix:lint": "eslint --fix .", "test": "jest && npm run check:type:ts", "test-all": "jest && npm run test-esm && npm run check", "test-esm": "node ./tests/esm-imports-test.mjs"}, "files": ["index.js", "lib/*.js", "esm.mjs", "typings/index.d.ts", "typings/esm.d.mts", "package-support.json"], "type": "commonjs", "main": "./index.js", "exports": {".": {"require": {"types": "./typings/index.d.ts", "default": "./index.js"}, "import": {"types": "./typings/esm.d.mts", "default": "./esm.mjs"}, "default": "./index.js"}, "./esm.mjs": {"types": "./typings/esm.d.mts", "import": "./esm.mjs"}}, "devDependencies": {"@eslint/js": "^8.56.0", "@types/jest": "^29.2.4", "@types/node": "^20.2.5", "eslint": "^8.30.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest": "^28.3.0", "eslint-plugin-jsdoc": "^48.1.0", "globals": "^13.24.0", "jest": "^29.3.1", "prettier": "^3.2.5", "prettier-plugin-jsdoc": "^1.3.0", "ts-jest": "^29.0.3", "tsd": "^0.31.0", "typescript": "^5.0.4", "typescript-eslint": "^7.0.1"}, "types": "typings/index.d.ts", "engines": {"node": ">=18"}, "support": true}