{"name": "metro-core", "version": "0.82.5", "description": "🚇 Metro's core package.", "main": "src/index.js", "exports": {".": "./src/index.js", "./package.json": "./package.json", "./private/*": "./src/*.js", "./src": "./src/index.js", "./src/*.js": "./src/*.js", "./src/*": "./src/*.js"}, "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "dependencies": {"flow-enums-runtime": "^0.0.6", "lodash.throttle": "^4.1.1", "metro-resolver": "0.82.5"}, "license": "MIT", "engines": {"node": ">=18.18"}}