/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<f12134ff61b5a0df05104369bd71b2d3>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Components/Switch/AndroidSwitchNativeComponent.js
 */

export * from "../../../src/private/specs_DEPRECATED/components/AndroidSwitchNativeComponent";
import AndroidSwitchNativeComponent from "../../../src/private/specs_DEPRECATED/components/AndroidSwitchNativeComponent";
declare const $$AndroidSwitchNativeComponent: typeof AndroidSwitchNativeComponent;
declare type $$AndroidSwitchNativeComponent = typeof $$AndroidSwitchNativeComponent;
export default $$AndroidSwitchNativeComponent;
