/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<3d88aac7abaa73e589fc442788985bda>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Components/ProgressBarAndroid/ProgressBarAndroidNativeComponent.js
 */

export * from "../../../src/private/specs_DEPRECATED/components/ProgressBarAndroidNativeComponent";
import ProgressBarAndroidNativeComponent from "../../../src/private/specs_DEPRECATED/components/ProgressBarAndroidNativeComponent";
declare const $$ProgressBarAndroidNativeComponent: typeof ProgressBarAndroidNativeComponent;
declare type $$ProgressBarAndroidNativeComponent = typeof $$ProgressBarAndroidNativeComponent;
export default $$ProgressBarAndroidNativeComponent;
