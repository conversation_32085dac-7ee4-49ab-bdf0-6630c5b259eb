/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<09f21486cfab11e52fb7ee57590d23cb>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Animated/components/AnimatedImage.js
 */

import type { AnimatedComponentType } from "../createAnimatedComponent";
import Image from "../../Image/Image";
import * as React from "react";
declare const $$AnimatedImage: AnimatedComponentType<React.JSX.LibraryManagedAttributes<typeof Image, React.ComponentProps<typeof Image>>, React.ComponentRef<typeof Image>>;
declare type $$AnimatedImage = typeof $$AnimatedImage;
export default $$AnimatedImage;
