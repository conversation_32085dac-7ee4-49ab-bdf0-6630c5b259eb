/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<dbb74f6add45d14478bc6462b5f6640d>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/Image/ImageResizeMode.js
 */

/**
 * ImageResizeMode defines valid values for different image resizing modes set
 * via the `resizeMode` style property on `<Image>`.
 */
export type ImageResizeMode = "center" | "contain" | "cover" | "repeat" | "stretch" | "none";
