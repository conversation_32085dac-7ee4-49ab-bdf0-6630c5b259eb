export interface Theme {
  name: 'light' | 'dark';
  colors: {
    // Background colors
    background: string;
    surface: string;
    card: string;
    
    // Text colors
    text: string;
    textSecondary: string;
    textMuted: string;
    
    // Primary colors
    primary: string;
    primaryDark: string;
    primaryLight: string;
    
    // Status colors
    success: string;
    warning: string;
    error: string;
    info: string;
    
    // Border and divider colors
    border: string;
    divider: string;
    
    // Interactive colors
    ripple: string;
    overlay: string;
    
    // Tab colors
    tabActive: string;
    tabInactive: string;
    tabBackground: string;
    
    // Status indicator colors
    statusConnected: string;
    statusDisconnected: string;
    statusConnecting: string;
  };
  
  // Animation durations
  animation: {
    fast: number;
    normal: number;
    slow: number;
  };
  
  // Spacing
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  
  // Border radius
  borderRadius: {
    sm: number;
    md: number;
    lg: number;
    xl: number;
  };
  
  // Shadows
  shadows: {
    small: {
      shadowColor: string;
      shadowOffset: { width: number; height: number };
      shadowOpacity: number;
      shadowRadius: number;
      elevation: number;
    };
    medium: {
      shadowColor: string;
      shadowOffset: { width: number; height: number };
      shadowOpacity: number;
      shadowRadius: number;
      elevation: number;
    };
  };
}

export const lightTheme: Theme = {
  name: 'light',
  colors: {
    background: '#f5f5f5',
    surface: '#ffffff',
    card: '#ffffff',
    
    text: '#333333',
    textSecondary: '#666666',
    textMuted: '#888888',
    
    primary: '#007AFF',
    primaryDark: '#0056CC',
    primaryLight: '#4DA3FF',
    
    success: '#4CAF50',
    warning: '#FFA500',
    error: '#F44336',
    info: '#2196F3',
    
    border: '#e0e0e0',
    divider: '#f0f0f0',
    
    ripple: 'rgba(0, 122, 255, 0.1)',
    overlay: 'rgba(0, 0, 0, 0.5)',
    
    tabActive: '#007AFF',
    tabInactive: '#666666',
    tabBackground: '#ffffff',
    
    statusConnected: '#4CAF50',
    statusDisconnected: '#F44336',
    statusConnecting: '#FFA500',
  },
  
  animation: {
    fast: 150,
    normal: 250,
    slow: 400,
  },
  
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
  },
  
  shadows: {
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 3.84,
      elevation: 5,
    },
  },
};

export const darkTheme: Theme = {
  name: 'dark',
  colors: {
    background: '#121212',
    surface: '#1e1e1e',
    card: '#2d2d2d',
    
    text: '#ffffff',
    textSecondary: '#b3b3b3',
    textMuted: '#888888',
    
    primary: '#4DA3FF',
    primaryDark: '#0056CC',
    primaryLight: '#80BFFF',
    
    success: '#66BB6A',
    warning: '#FFB74D',
    error: '#EF5350',
    info: '#42A5F5',
    
    border: '#404040',
    divider: '#2d2d2d',
    
    ripple: 'rgba(77, 163, 255, 0.1)',
    overlay: 'rgba(0, 0, 0, 0.7)',
    
    tabActive: '#4DA3FF',
    tabInactive: '#b3b3b3',
    tabBackground: '#1e1e1e',
    
    statusConnected: '#66BB6A',
    statusDisconnected: '#EF5350',
    statusConnecting: '#FFB74D',
  },
  
  animation: {
    fast: 150,
    normal: 250,
    slow: 400,
  },
  
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
  },
  
  shadows: {
    small: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.3,
      shadowRadius: 2,
      elevation: 2,
    },
    medium: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 3.84,
      elevation: 5,
    },
  },
};
