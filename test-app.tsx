import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { ThemeProvider, useTheme } from './contexts/ThemeContext';
import { AnimatedButton } from './components/AnimatedButton';

const TestContent: React.FC = () => {
  const { theme, isDark, toggleTheme } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <Text style={[styles.title, { color: theme.colors.text }]}>
        🎨 Theme Test
      </Text>
      
      <Text style={[styles.subtitle, { color: theme.colors.textSecondary }]}>
        Current theme: {isDark ? '🌙 Dark' : '☀️ Light'}
      </Text>

      <View style={styles.buttonContainer}>
        <AnimatedButton
          title={`Switch to ${isDark ? 'Light' : 'Dark'}`}
          onPress={toggleTheme}
          variant="primary"
        />
        
        <AnimatedButton
          title="Test Success"
          onPress={() => console.log('Success!')}
          variant="success"
        />
        
        <AnimatedButton
          title="Test Secondary"
          onPress={() => console.log('Secondary!')}
          variant="secondary"
        />
      </View>
    </View>
  );
};

export const TestApp: React.FC = () => {
  return (
    <ThemeProvider>
      <TestContent />
    </ThemeProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 30,
  },
  buttonContainer: {
    width: '100%',
    gap: 15,
  },
});
