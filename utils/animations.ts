import { Animated, Easing, LayoutAnimation, Platform, UIManager } from 'react-native';

// Enable LayoutAnimation on Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

export class AnimationUtils {
  // Fade animations
  static fadeIn(animatedValue: Animated.Value, duration: number = 250): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: 1,
      duration,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    });
  }

  static fadeOut(animatedValue: Animated.Value, duration: number = 250): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: Easing.in(Easing.cubic),
      useNativeDriver: true,
    });
  }

  // Scale animations
  static scaleIn(animatedValue: Animated.Value, duration: number = 200): Animated.CompositeAnimation {
    return Animated.spring(animatedValue, {
      toValue: 1,
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    });
  }

  static scaleOut(animatedValue: Animated.Value, duration: number = 200): Animated.CompositeAnimation {
    return Animated.spring(animatedValue, {
      toValue: 0,
      tension: 100,
      friction: 8,
      useNativeDriver: true,
    });
  }

  // Button press animation
  static buttonPress(animatedValue: Animated.Value): Animated.CompositeAnimation {
    return Animated.sequence([
      Animated.timing(animatedValue, {
        toValue: 0.95,
        duration: 100,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 100,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
    ]);
  }

  // Slide animations
  static slideInFromRight(animatedValue: Animated.Value, duration: number = 300): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    });
  }

  static slideOutToRight(animatedValue: Animated.Value, screenWidth: number, duration: number = 300): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: screenWidth,
      duration,
      easing: Easing.in(Easing.cubic),
      useNativeDriver: true,
    });
  }

  static slideInFromLeft(animatedValue: Animated.Value, duration: number = 300): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: 0,
      duration,
      easing: Easing.out(Easing.cubic),
      useNativeDriver: true,
    });
  }

  static slideOutToLeft(animatedValue: Animated.Value, screenWidth: number, duration: number = 300): Animated.CompositeAnimation {
    return Animated.timing(animatedValue, {
      toValue: -screenWidth,
      duration,
      easing: Easing.in(Easing.cubic),
      useNativeDriver: true,
    });
  }

  // Rotation animation
  static rotate(animatedValue: Animated.Value, duration: number = 1000): Animated.CompositeAnimation {
    return Animated.loop(
      Animated.timing(animatedValue, {
        toValue: 1,
        duration,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    );
  }

  // Pulse animation
  static pulse(animatedValue: Animated.Value, duration: number = 1000): Animated.CompositeAnimation {
    return Animated.loop(
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1.1,
          duration: duration / 2,
          easing: Easing.inOut(Easing.cubic),
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: duration / 2,
          easing: Easing.inOut(Easing.cubic),
          useNativeDriver: true,
        }),
      ])
    );
  }

  // Layout animations
  static configureLayoutAnimation(duration: number = 300) {
    LayoutAnimation.configureNext({
      duration,
      create: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
      update: {
        type: LayoutAnimation.Types.easeInEaseOut,
      },
      delete: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
    });
  }

  // Stagger animation
  static stagger(animations: Animated.CompositeAnimation[], delay: number = 100): Animated.CompositeAnimation {
    return Animated.stagger(delay, animations);
  }

  // Parallel animation
  static parallel(animations: Animated.CompositeAnimation[]): Animated.CompositeAnimation {
    return Animated.parallel(animations);
  }

  // Sequence animation
  static sequence(animations: Animated.CompositeAnimation[]): Animated.CompositeAnimation {
    return Animated.sequence(animations);
  }
}

// Custom hooks for common animations
export const useAnimatedValue = (initialValue: number = 0) => {
  return new Animated.Value(initialValue);
};

export const useFadeAnimation = (initialValue: number = 0) => {
  const fadeAnim = useAnimatedValue(initialValue);
  
  const fadeIn = (duration?: number) => AnimationUtils.fadeIn(fadeAnim, duration);
  const fadeOut = (duration?: number) => AnimationUtils.fadeOut(fadeAnim, duration);
  
  return { fadeAnim, fadeIn, fadeOut };
};

export const useScaleAnimation = (initialValue: number = 1) => {
  const scaleAnim = useAnimatedValue(initialValue);
  
  const scaleIn = (duration?: number) => AnimationUtils.scaleIn(scaleAnim, duration);
  const scaleOut = (duration?: number) => AnimationUtils.scaleOut(scaleAnim, duration);
  const buttonPress = () => AnimationUtils.buttonPress(scaleAnim);
  
  return { scaleAnim, scaleIn, scaleOut, buttonPress };
};
