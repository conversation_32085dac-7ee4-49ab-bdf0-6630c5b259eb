import { Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';

export interface NetworkConfig {
  baseUrl: string;
  isLocalhost: boolean;
  detectedIp?: string;
  networkType?: string;
}

export class NetworkUtils {
  private static instance: NetworkUtils;
  private cachedConfig: NetworkConfig | null = null;

  static getInstance(): NetworkUtils {
    if (!NetworkUtils.instance) {
      NetworkUtils.instance = new NetworkUtils();
    }
    return NetworkUtils.instance;
  }

  /**
   * Detecteert het juiste IP-adres voor de ontwikkelserver
   */
  async detectDevelopmentServerUrl(port: number = 3000): Promise<NetworkConfig> {
    try {
      // Controleer netwerk connectiviteit
      const netInfo = await NetInfo.fetch();
      
      if (!netInfo.isConnected) {
        throw new Error('Geen netwerkverbinding beschikbaar');
      }

      // Voor iOS simulator kan localhost werken
      if (Platform.OS === 'ios' && __DEV__) {
        const isSimulator = await this.isIOSSimulator();
        if (isSimulator) {
          const config: NetworkConfig = {
            baseUrl: `http://localhost:${port}`,
            isLocalhost: true,
            networkType: netInfo.type
          };
          
          // Test de verbinding
          const isReachable = await this.testConnection(config.baseUrl);
          if (isReachable) {
            this.cachedConfig = config;
            return config;
          }
        }
      }

      // Voor fysieke apparaten: detecteer lokaal IP-adres
      const localIp = await this.getLocalIPAddress();
      const config: NetworkConfig = {
        baseUrl: `http://${localIp}:${port}`,
        isLocalhost: false,
        detectedIp: localIp,
        networkType: netInfo.type
      };

      // Test de verbinding
      const isReachable = await this.testConnection(config.baseUrl);
      if (!isReachable) {
        throw new Error(`Server niet bereikbaar op ${config.baseUrl}`);
      }

      this.cachedConfig = config;
      return config;

    } catch (error) {
      console.error('Network detection error:', error);
      throw error;
    }
  }

  /**
   * Test of een URL bereikbaar is
   */
  async testConnection(url: string, timeout: number = 5000): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(`${url}/health`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
        }
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      console.log(`Connection test failed for ${url}:`, error);
      return false;
    }
  }

  /**
   * Detecteert lokaal IP-adres van de ontwikkelmachine
   */
  private async getLocalIPAddress(): Promise<string> {
    try {
      // Probeer verschillende methoden om het IP-adres te krijgen
      const possibleIPs = [
        '192.168.1.', // Meest voorkomende router range
        '192.168.0.',
        '10.0.0.',
        '172.16.',
      ];

      // In een echte implementatie zou je het IP-adres van de ontwikkelmachine
      // kunnen doorgeven via environment variables of een configuratiebestand
      const devMachineIp = await this.scanForDevelopmentServer(possibleIPs);
      
      if (!devMachineIp) {
        throw new Error('Kon ontwikkelserver niet vinden op het lokale netwerk');
      }

      return devMachineIp;
    } catch (error) {
      throw new Error(`IP-adres detectie gefaald: ${error.message}`);
    }
  }

  /**
   * Scant het lokale netwerk voor de ontwikkelserver
   */
  private async scanForDevelopmentServer(ipRanges: string[]): Promise<string | null> {
    // Voor demo doeleinden - in productie zou je dit anders implementeren
    // Je kunt het IP-adres ook handmatig configureren of via QR-code scannen
    
    // Tijdelijke oplossing: gebruik een vast IP-adres dat je handmatig configureert
    const manualIp = await this.getManuallyConfiguredIP();
    if (manualIp) {
      return manualIp;
    }

    // Als geen handmatige configuratie, probeer veelvoorkomende IP-adressen
    const commonIPs = [
      '*************', // Vervang met jouw werkelijke IP
      '*************',
      '**********'
    ];

    for (const ip of commonIPs) {
      const isReachable = await this.testConnection(`http://${ip}:3000`);
      if (isReachable) {
        return ip;
      }
    }

    return null;
  }

  private async getManuallyConfiguredIP(): Promise<string | null> {
    // Hier zou je een opgeslagen IP-adres uit AsyncStorage kunnen ophalen
    // Voor nu retourneren we null om de scan te laten doorgaan
    return null;
  }

  private async isIOSSimulator(): Promise<boolean> {
    if (Platform.OS !== 'ios') return false;
    
    // In een echte implementatie zou je react-native-device-info gebruiken
    // Voor nu assumeren we dat het een fysiek apparaat is als we hier komen
    return false;
  }

  getCachedConfig(): NetworkConfig | null {
    return this.cachedConfig;
  }

  clearCache(): void {
    this.cachedConfig = null;
  }
}
