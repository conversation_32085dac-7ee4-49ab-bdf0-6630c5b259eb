[INFO] Scanning for projects...
[INFO] 
[INFO] --------------------------< com.example:app >--------------------------
[INFO] Building app 0.0.1-SNAPSHOT
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- maven-resources-plugin:3.2.0:resources (default-resources) @ app ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] Copying 1 resource
[INFO] Copying 32 resources
[INFO] 
[INFO] --- maven-compiler-plugin:3.8.1:compile (default-compile) @ app ---
[INFO] Changes detected - recompiling the module!
[INFO] Compiling 184 source files to /Users/<USER>/repos/example/app-api/target/classes
[ERROR] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/services/impl/BeneficialOwnerServiceImpl.java:[216,74] cannot find symbol
[ERROR]   symbol:   method findVerificationsForBeneficialOwner(java.lang.String)
[ERROR]   location: variable beneficialOwnerVerificationRepository of type com.example.infrastructure.repositories.BeneficialOwnerVerificationRepository
[ERROR] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/services/impl/BeneficialOwnerServiceImpl.java:[240,76] cannot find symbol
[ERROR]   symbol:   method findVerificationsForBeneficialOwner(java.lang.String)
[ERROR]   location: variable beneficialOwnerVerificationRepository of type com.example.infrastructure.repositories.BeneficialOwnerVerificationRepository
[INFO] ------------------------------------------------------------------------
[INFO] BUILD FAILURE
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  3.651 s
[INFO] Finished at: 2023-07-20T15:22:25+02:00
[INFO] ------------------------------------------------------------------------
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-compiler-plugin:3.8.1:compile (default-compile) on project app: Compilation failure: Compilation failure: 
[ERROR] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/services/impl/BeneficialOwnerServiceImpl.java:[216,74] cannot find symbol
[ERROR]   symbol:   method findVerificationsForBeneficialOwner(java.lang.String)
[ERROR]   location: variable beneficialOwnerVerificationRepository of type com.example.infrastructure.repositories.BeneficialOwnerVerificationRepository
[ERROR] /Users/<USER>/repos/example/app-api/src/main/java/com/example/core/services/impl/BeneficialOwnerServiceImpl.java:[240,76] cannot find symbol
[ERROR]   symbol:   method findVerificationsForBeneficialOwner(java.lang.String)
[ERROR]   location: variable beneficialOwnerVerificationRepository of type com.example.infrastructure.repositories.BeneficialOwnerVerificationRepository
[ERROR] -> [Help 1]
[ERROR] 
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR] 
[ERROR] For more information about the errors and possible solutions, please read the following articles:
[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MojoFailureException
