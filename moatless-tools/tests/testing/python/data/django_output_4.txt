>>>>> Run tests
Testing against Django installed in '/testbed/django'
Importing application admin_views
Found 344 test(s).
Skipping setup of unused database(s): other.
Operations to perform:
  Synchronize unmigrated apps: admin_views, auth, contenttypes, messages, sessions, staticfiles
  Apply all migrations: admin, sites
Synchronizing apps without migrations:
  Creating tables...
    Creating table django_content_type
    Creating table auth_permission
    Creating table auth_group
    Creating table auth_user
    Creating table django_session
    Creating table admin_views_section
    Creating table admin_views_article
    Creating table admin_views_book
    Creating table admin_views_promo
    Creating table admin_views_chapter
    Creating table admin_views_chapterxtra1
    Creating table admin_views_chapterxtra2
    Creating table admin_views_rowlevelchangepermissionmodel
    Creating table admin_views_customarticle
    Creating table admin_views_modelwithstringprimarykey
    Creating table admin_views_color
    Creating table admin_views_thing
    Creating table admin_views_actor
    Creating table admin_views_inquisition
    Creating table admin_views_sketch
    Creating table admin_views_character
    Creating table admin_views_stumpjoke
    Creating table admin_views_fabric
    Creating table admin_views_person
    Creating table admin_views_persona
    Creating table admin_views_account
    Creating table admin_views_fooaccount
    Creating table admin_views_baraccount
    Creating table admin_views_subscriber
    Creating table admin_views_externalsubscriber
    Creating table admin_views_oldsubscriber
    Creating table admin_views_media
    Creating table admin_views_podcast
    Creating table admin_views_vodcast
    Creating table admin_views_parent
    Creating table admin_views_child
    Creating table admin_views_pkchild
    Creating table admin_views_toy
    Creating table admin_views_emptymodel
    Creating table admin_views_gallery
    Creating table admin_views_picture
    Creating table admin_views_language
    Creating table admin_views_title
    Creating table admin_views_titletranslation
    Creating table admin_views_recommender
    Creating table admin_views_recommendation
    Creating table admin_views_collector
    Creating table admin_views_widget
    Creating table admin_views_doohickey
    Creating table admin_views_grommet
    Creating table admin_views_whatsit
    Creating table admin_views_doodad
    Creating table admin_views_fancydoodad
    Creating table admin_views_category
    Creating table admin_views_link
    Creating table admin_views_prepopulatedpost
    Creating table admin_views_prepopulatedsubpost
    Creating table admin_views_post
    Creating table admin_views_gadget
    Creating table admin_views_villain
    Creating table admin_views_supervillain
    Creating table admin_views_funkytag
    Creating table admin_views_plot
    Creating table admin_views_plotdetails
    Creating table admin_views_secrethideout
    Creating table admin_views_supersecrethideout
    Creating table admin_views_bookmark
    Creating table admin_views_cyclicone
    Creating table admin_views_cyclictwo
    Creating table admin_views_topping
    Creating table admin_views_pizza
    Creating table admin_views_album
    Creating table admin_views_song
    Creating table admin_views_employee
    Creating table admin_views_workhour
    Creating table admin_views_manager
    Creating table admin_views_bonus
    Creating table admin_views_question
    Creating table admin_views_answer
    Creating table admin_views_reservation
    Creating table admin_views_fooddelivery
    Creating table admin_views_coverletter
    Creating table admin_views_paper
    Creating table admin_views_shortmessage
    Creating table admin_views_telegram
    Creating table admin_views_story
    Creating table admin_views_otherstory
    Creating table admin_views_complexsortedperson
    Creating table admin_views_pluggablesearchperson
    Creating table admin_views_prepopulatedpostlargeslug
    Creating table admin_views_adminorderedfield
    Creating table admin_views_adminorderedmodelmethod
    Creating table admin_views_adminorderedadminmethod
    Creating table admin_views_adminorderedcallable
    Creating table admin_views_report
    Creating table admin_views_mainprepopulated
    Creating table admin_views_relatedprepopulated
    Creating table admin_views_unorderedobject
    Creating table admin_views_undeletableobject
    Creating table admin_views_unchangeableobject
    Creating table admin_views_usermessenger
    Creating table admin_views_simple
    Creating table admin_views_choice
    Creating table admin_views_parentwithdependentchildren
    Creating table admin_views_dependentchild
    Creating table admin_views_filteredmanager
    Creating table admin_views_emptymodelvisible
    Creating table admin_views_emptymodelhidden
    Creating table admin_views_emptymodelmixin
    Creating table admin_views_state
    Creating table admin_views_city
    Creating table admin_views_restaurant
    Creating table admin_views_worker
    Creating table admin_views_referencedbyparent
    Creating table admin_views_parentwithfk
    Creating table admin_views_childofreferer
    Creating table admin_views_inlinereferer
    Creating table admin_views_referencedbyinline
    Creating table admin_views_inlinereference
    Creating table admin_views_recipe
    Creating table admin_views_ingredient
    Creating table admin_views_recipeingredient
    Creating table admin_views_notreferenced
    Creating table admin_views_explicitlyprovidedpk
    Creating table admin_views_implicitlygeneratedpk
    Creating table admin_views_referencedbygenrel
    Creating table admin_views_genrelreference
    Creating table admin_views_parentwithuuidpk
    Creating table admin_views_relatedwithuuidpkmodel
    Creating table admin_views_author
    Creating table admin_views_authorship
    Creating table admin_views_readonlyrelatedfield
    Creating table admin_views_héllo
    Running deferred SQL...
Running migrations:
  Applying admin.0001_initial... OK
  Applying admin.0002_logentry_remove_auto_add... OK
  Applying admin.0003_logentry_add_action_flag_choices... OK
  Applying sites.0001_initial... OK
  Applying sites.0002_alter_domain_unique... OK
Creating test database for alias 'default' ('file:memorydb_default?mode=memory&cache=shared')...
System check identified no issues (1 silenced).
test_add_model_modeladmin_defer_qs (admin_views.tests.AdminCustomQuerysetTest) ... ok
test_add_model_modeladmin_only_qs (admin_views.tests.AdminCustomQuerysetTest) ... ok
test_change_view (admin_views.tests.AdminCustomQuerysetTest) ... ok
test_changelist_view (admin_views.tests.AdminCustomQuerysetTest) ... ok
test_changelist_view_count_queries (admin_views.tests.AdminCustomQuerysetTest) ... ok
test_edit_model_modeladmin_defer_qs (admin_views.tests.AdminCustomQuerysetTest) ... ok
test_edit_model_modeladmin_only_qs (admin_views.tests.AdminCustomQuerysetTest) ... ok
test_history_view_custom_qs (admin_views.tests.AdminCustomQuerysetTest)
Custom querysets are considered for the admin history view. ... ok
test_should_be_able_to_edit_related_objects_on_add_view (admin_views.tests.AdminCustomSaveRelatedTests) ... ok
test_should_be_able_to_edit_related_objects_on_change_view (admin_views.tests.AdminCustomSaveRelatedTests) ... ok
test_should_be_able_to_edit_related_objects_on_changelist_view (admin_views.tests.AdminCustomSaveRelatedTests) ... ok
test_change_password_template (admin_views.tests.AdminCustomTemplateTests) ... ok
test_custom_model_admin_templates (admin_views.tests.AdminCustomTemplateTests) ... ok
test_extended_bodyclass_change_list (admin_views.tests.AdminCustomTemplateTests)
The admin/change_list.html' template uses block.super ... ok
test_extended_bodyclass_template_change_form (admin_views.tests.AdminCustomTemplateTests)
The admin/change_form.html template uses block.super in the ... ok
test_extended_bodyclass_template_delete_confirmation (admin_views.tests.AdminCustomTemplateTests)
The admin/delete_confirmation.html template uses ... ok
test_extended_bodyclass_template_delete_selected_confirmation (admin_views.tests.AdminCustomTemplateTests)
The admin/delete_selected_confirmation.html template uses ... ok
test_extended_bodyclass_template_index (admin_views.tests.AdminCustomTemplateTests)
The admin/index.html template uses block.super in the bodyclass block. ... ok
test_extended_bodyclass_template_login (admin_views.tests.AdminCustomTemplateTests)
The admin/login.html template uses block.super in the ... ok
test_filter_with_custom_template (admin_views.tests.AdminCustomTemplateTests)
A custom template can be used to render an admin filter. ... ok
test_filters (admin_views.tests.AdminDocsTest) ... ok
test_tags (admin_views.tests.AdminDocsTest) ... ok
test_inline (admin_views.tests.AdminInheritedInlinesTest)
Inline models which inherit from a common parent are correctly handled. ... ok
test_form_has_multipart_enctype (admin_views.tests.AdminInlineFileUploadTest) ... ok
test_inline_file_upload_edit_validation_error_post (admin_views.tests.AdminInlineFileUploadTest)
Inline file uploads correctly display prior data (#10002). ... ok
test_char_pk_inline (admin_views.tests.AdminInlineTests)
A model with a character PK can be saved as inlines. Regression for #10992 ... ok
test_explicit_autofield_inline (admin_views.tests.AdminInlineTests)
A model with an explicit autofield primary key can be saved as inlines. Regression for #8093 ... ok
test_inherited_inline (admin_views.tests.AdminInlineTests)
An inherited model can be saved as inlines. Regression for #11042 ... ok
test_integer_pk_inline (admin_views.tests.AdminInlineTests)
A model with an integer PK can be saved as inlines. Regression for #10992 ... ok
test_ordered_inline (admin_views.tests.AdminInlineTests)
An inline with an editable ordering fields is updated correctly. ... ok
test_simple_inline (admin_views.tests.AdminInlineTests)
A simple model can be saved as inlines ... ok
test_js_minified_only_if_debug_is_false (admin_views.tests.AdminJavaScriptTest)
The minified versions of the JS files are only used when DEBUG is False. ... ok
test_add_view (admin_views.tests.AdminKeepChangeListFiltersTests) ... ok
test_add_view_without_preserved_filters (admin_views.tests.AdminKeepChangeListFiltersTests) ... ok
test_assert_url_equal (admin_views.tests.AdminKeepChangeListFiltersTests) ... ok
test_change_view (admin_views.tests.AdminKeepChangeListFiltersTests) ... ok
test_change_view_without_preserved_filters (admin_views.tests.AdminKeepChangeListFiltersTests) ... ok
test_changelist_view (admin_views.tests.AdminKeepChangeListFiltersTests) ... ok
test_delete_view (admin_views.tests.AdminKeepChangeListFiltersTests) ... ok
test_url_prefix (admin_views.tests.AdminKeepChangeListFiltersTests) ... ok
test_beginning_matches (admin_views.tests.AdminSearchTest) ... ok
test_exact_matches (admin_views.tests.AdminSearchTest) ... ok
test_no_total_count (admin_views.tests.AdminSearchTest)
#8408 -- "Show all" should be displayed instead of the total count if ... ok
test_pluggable_search (admin_views.tests.AdminSearchTest) ... ok
test_reset_link (admin_views.tests.AdminSearchTest)
Test presence of reset link in search bar ("1 result (_x total_)"). ... ok
test_search_on_sibling_models (admin_views.tests.AdminSearchTest)
A search that mentions sibling models ... ok
test_search_with_spaces (admin_views.tests.AdminSearchTest) ... ok
test_with_fk_to_field (admin_views.tests.AdminSearchTest)
The to_field GET parameter is preserved when a search is performed. ... ok
test_known_url_missing_slash_redirects_login_if_not_authenticated (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_known_url_missing_slash_redirects_with_slash_if_not_authenticated_without_final_catch_all_view (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_known_url_redirects_login_if_not_authenticated (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_known_url_redirects_login_if_not_authenticated_without_final_catch_all_view (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_missing_slash_append_slash_false (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_missing_slash_append_slash_false_without_final_catch_all_view (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_missing_slash_append_slash_true (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_missing_slash_append_slash_true_force_script_name (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_missing_slash_append_slash_true_non_staff_user (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_missing_slash_append_slash_true_script_name (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_missing_slash_append_slash_true_unknown_url (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_missing_slash_append_slash_true_unknown_url_without_final_catch_all_view (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_missing_slash_append_slash_true_without_final_catch_all_view (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_non_admin_url_404_if_not_authenticated (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_non_admin_url_shares_url_prefix (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_non_admin_url_shares_url_prefix_without_final_catch_all_view (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_single_model_no_append_slash (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_unknown_url_404_if_authenticated (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_unknown_url_404_if_authenticated_without_final_catch_all_view (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_unknown_url_404_if_not_authenticated_without_final_catch_all_view (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_unknown_url_redirects_login_if_not_authenticated (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_unkown_url_without_trailing_slash_if_not_authenticated (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_unkown_url_without_trailing_slash_if_not_authenticated_without_final_catch_all_view (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_url_without_trailing_slash_if_not_authenticated (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_url_without_trailing_slash_if_not_authenticated_without_final_catch_all_view (admin_views.tests.AdminSiteFinalCatchAllPatternTests) ... ok
test_message_debug (admin_views.tests.AdminUserMessageTest) ... ok
test_message_error (admin_views.tests.AdminUserMessageTest) ... ok
test_message_extra_tags (admin_views.tests.AdminUserMessageTest) ... ok
test_message_info (admin_views.tests.AdminUserMessageTest) ... ok
test_message_success (admin_views.tests.AdminUserMessageTest) ... ok
test_message_warning (admin_views.tests.AdminUserMessageTest) ... ok
test_add_with_GET_args (admin_views.tests.AdminViewBasicTest) ... ok
test_adminsite_display_site_url (admin_views.tests.AdminViewBasicTest)
#13749 - Admin should display link to front-end site 'View site' ... ok
test_allowed_filtering_15103 (admin_views.tests.AdminViewBasicTest)
Regressions test for ticket 15103 - filtering on fields defined in a ... ok
test_allows_attributeerror_to_bubble_up (admin_views.tests.AdminViewBasicTest)
AttributeErrors are allowed to bubble when raised inside a change list ... ok
test_app_index_context (admin_views.tests.AdminViewBasicTest) ... ok
test_basic_add_GET (admin_views.tests.AdminViewBasicTest)
A smoke test to ensure GET on the add_view works. ... ok
test_basic_add_POST (admin_views.tests.AdminViewBasicTest)
A smoke test to ensure POST on add_view works. ... ok
test_basic_edit_GET (admin_views.tests.AdminViewBasicTest)
A smoke test to ensure GET on the change_view works. ... ok
test_basic_edit_GET_old_url_redirect (admin_views.tests.AdminViewBasicTest)
The change URL changed in Django 1.9, but the old one still redirects. ... ok
test_basic_edit_GET_string_PK (admin_views.tests.AdminViewBasicTest)
GET on the change_view (when passing a string as the PK argument for a ... ok
test_basic_edit_POST (admin_views.tests.AdminViewBasicTest)
A smoke test to ensure POST on edit_view works. ... ok
test_basic_inheritance_GET_string_PK (admin_views.tests.AdminViewBasicTest)
GET on the change_view (for inherited models) redirects to the index ... ok
test_change_list_column_field_classes (admin_views.tests.AdminViewBasicTest) ... ok
test_change_list_null_boolean_display (admin_views.tests.AdminViewBasicTest) ... ok
test_change_list_sorting_callable (admin_views.tests.AdminViewBasicTest)
Ensure we can sort on a list_display field that is a callable ... ok
test_change_list_sorting_callable_query_expression (admin_views.tests.AdminViewBasicTest)
Query expressions may be used for admin_order_field. ... ok
test_change_list_sorting_callable_query_expression_reverse (admin_views.tests.AdminViewBasicTest) ... ok
test_change_list_sorting_model (admin_views.tests.AdminViewBasicTest)
Ensure we can sort on a list_display field that is a Model method ... ok
test_change_list_sorting_model_admin (admin_views.tests.AdminViewBasicTest)
Ensure we can sort on a list_display field that is a ModelAdmin method ... ok
test_change_list_sorting_model_admin_reverse (admin_views.tests.AdminViewBasicTest)
Ensure we can sort on a list_display field that is a ModelAdmin ... ok
test_change_list_sorting_model_meta (admin_views.tests.AdminViewBasicTest) ... ok
test_change_list_sorting_multiple (admin_views.tests.AdminViewBasicTest) ... ok
test_change_list_sorting_override_model_admin (admin_views.tests.AdminViewBasicTest) ... ok
test_change_list_sorting_preserve_queryset_ordering (admin_views.tests.AdminViewBasicTest)
If no ordering is defined in `ModelAdmin.ordering` or in the query ... ok
test_change_list_sorting_property (admin_views.tests.AdminViewBasicTest)
Sort on a list_display field that is a property (column 10 is ... ok
test_change_view_logs_m2m_field_changes (admin_views.tests.AdminViewBasicTest)
Changes to ManyToManyFields are included in the object's history. ... ok
test_change_view_subtitle_per_object (admin_views.tests.AdminViewBasicTest) ... ok
test_change_view_with_show_delete_extra_context (admin_views.tests.AdminViewBasicTest)
The 'show_delete' context variable in the admin's change view controls ... ok
test_changelist_with_no_change_url (admin_views.tests.AdminViewBasicTest)
ModelAdmin.changelist_view shouldn't result in a NoReverseMatch if url ... ok
test_date_hierarchy_empty_queryset (admin_views.tests.AdminViewBasicTest) ... ok
test_date_hierarchy_local_date_differ_from_utc (admin_views.tests.AdminViewBasicTest) ... ok
test_date_hierarchy_timezone_dst (admin_views.tests.AdminViewBasicTest) ... ok
test_disallowed_filtering (admin_views.tests.AdminViewBasicTest) ... ok
test_disallowed_to_field (admin_views.tests.AdminViewBasicTest) ... ok
test_display_decorator_with_boolean_and_empty_value (admin_views.tests.AdminViewBasicTest) ... ok
test_edit_save_as (admin_views.tests.AdminViewBasicTest)
Test "save as". ... ok
test_edit_save_as_delete_inline (admin_views.tests.AdminViewBasicTest)
Should be able to "Save as new" while also deleting an inline. ... ok
test_formset_kwargs_can_be_overridden (admin_views.tests.AdminViewBasicTest) ... ok
test_get_sortable_by_columns_subset (admin_views.tests.AdminViewBasicTest) ... ok
test_get_sortable_by_no_column (admin_views.tests.AdminViewBasicTest) ... ok
test_has_related_field_in_list_display_fk (admin_views.tests.AdminViewBasicTest)
Joins shouldn't be performed for <FK>_id fields in list display. ... ok
test_has_related_field_in_list_display_o2o (admin_views.tests.AdminViewBasicTest)
Joins shouldn't be performed for <O2O>_id fields in list display. ... ok
test_hide_change_password (admin_views.tests.AdminViewBasicTest)
Tests if the "change password" link in the admin is hidden if the User ... ok
test_i18n_language_non_english_default (admin_views.tests.AdminViewBasicTest)
Check if the JavaScript i18n view returns an empty language catalog ... ok
test_i18n_language_non_english_fallback (admin_views.tests.AdminViewBasicTest)
Makes sure that the fallback language is still working properly ... ok
test_incorrect_lookup_parameters (admin_views.tests.AdminViewBasicTest)
Ensure incorrect lookup parameters are handled gracefully. ... ok
test_invalid_appindex_url (admin_views.tests.AdminViewBasicTest)
#21056 -- URL reversing shouldn't work for nonexistent apps. ... ok
test_isnull_lookups (admin_views.tests.AdminViewBasicTest)
Ensure is_null is handled correctly. ... ok
test_jsi18n_format_fallback (admin_views.tests.AdminViewBasicTest)
The JavaScript i18n view doesn't return localized date/time formats ... ok
test_jsi18n_with_context (admin_views.tests.AdminViewBasicTest) ... ok
test_limited_filter (admin_views.tests.AdminViewBasicTest)
Ensure admin changelist filters do not contain objects excluded via limit_choices_to. ... ok
test_logout_and_password_change_URLs (admin_views.tests.AdminViewBasicTest) ... ok
test_multiple_sort_same_field (admin_views.tests.AdminViewBasicTest) ... ok
test_named_group_field_choices_change_list (admin_views.tests.AdminViewBasicTest)
Ensures the admin changelist shows correct values in the relevant column ... ok
test_named_group_field_choices_filter (admin_views.tests.AdminViewBasicTest)
Ensures the filter UI shows correctly when at least one named group has ... ok
test_popup_add_POST (admin_views.tests.AdminViewBasicTest)
Ensure http response from a popup is properly escaped. ... ok
test_popup_dismiss_related (admin_views.tests.AdminViewBasicTest)
Regression test for ticket 20664 - ensure the pk is properly quoted. ... ok
test_relation_spanning_filters (admin_views.tests.AdminViewBasicTest) ... ok
test_render_views_no_subtitle (admin_views.tests.AdminViewBasicTest) ... ok
test_resolve_admin_views (admin_views.tests.AdminViewBasicTest) ... ok
test_sort_indicators_admin_order (admin_views.tests.AdminViewBasicTest)
The admin shows default sort indicators for all kinds of 'ordering' ... ok
test_sortable_by_columns_subset (admin_views.tests.AdminViewBasicTest) ... ok
test_sortable_by_no_column (admin_views.tests.AdminViewBasicTest) ... ok
test_trailing_slash_required (admin_views.tests.AdminViewBasicTest)
If you leave off the trailing slash, app should redirect and add it. ... ok
test_view_subtitle_per_object (admin_views.tests.AdminViewBasicTest) ... ok
test_cyclic (admin_views.tests.AdminViewDeletedObjectsTest)
Cyclic relationships should still cause each object to only be ... ok
test_delete_view_uses_get_deleted_objects (admin_views.tests.AdminViewDeletedObjectsTest)
The delete view uses ModelAdmin.get_deleted_objects(). ... ok
test_generic_relations (admin_views.tests.AdminViewDeletedObjectsTest)
If a deleted object has GenericForeignKeys pointing to it, ... ok
test_generic_relations_with_related_query_name (admin_views.tests.AdminViewDeletedObjectsTest)
If a deleted object has GenericForeignKey with ... ok
test_inheritance (admin_views.tests.AdminViewDeletedObjectsTest)
In the case of an inherited model, if either the child or ... ok
test_multiple_fkeys_to_same_instance (admin_views.tests.AdminViewDeletedObjectsTest)
If a deleted object has two relationships pointing to it from ... ok
test_multiple_fkeys_to_same_model (admin_views.tests.AdminViewDeletedObjectsTest)
If a deleted object has two relationships from another model, ... ok
test_nesting (admin_views.tests.AdminViewDeletedObjectsTest)
Objects should be nested to display the relationships that ... ok
test_not_registered (admin_views.tests.AdminViewDeletedObjectsTest) ... ok
test_perms_needed (admin_views.tests.AdminViewDeletedObjectsTest) ... ok
test_post_delete_protected (admin_views.tests.AdminViewDeletedObjectsTest)
A POST request to delete protected objects should display the page ... ok
test_post_delete_restricted (admin_views.tests.AdminViewDeletedObjectsTest) ... ok
test_protected (admin_views.tests.AdminViewDeletedObjectsTest) ... ok
test_restricted (admin_views.tests.AdminViewDeletedObjectsTest) ... ok
test_change_form_URL_has_correct_value (admin_views.tests.AdminViewFormUrlTest)
change_view has form_url in response.context ... ok
test_initial_data_can_be_overridden (admin_views.tests.AdminViewFormUrlTest)
The behavior for setting initial form data can be overridden in the ... ok
test_changelist_input_html (admin_views.tests.AdminViewListEditable) ... ok
test_custom_pk (admin_views.tests.AdminViewListEditable) ... ok
test_inheritance (admin_views.tests.AdminViewListEditable) ... ok
test_inheritance_2 (admin_views.tests.AdminViewListEditable) ... ok
test_list_editable_action_choices (admin_views.tests.AdminViewListEditable) ... ok
test_list_editable_action_submit (admin_views.tests.AdminViewListEditable) ... ok
test_list_editable_ordering (admin_views.tests.AdminViewListEditable) ... ok
test_list_editable_pagination (admin_views.tests.AdminViewListEditable)
Pagination works for list_editable items. ... ok
test_list_editable_popup (admin_views.tests.AdminViewListEditable)
Fields should not be list-editable in popups. ... ok
test_non_field_errors (admin_views.tests.AdminViewListEditable)
Non-field errors are displayed for each of the forms in the ... ok
test_non_form_errors (admin_views.tests.AdminViewListEditable) ... ok
test_non_form_errors_is_errorlist (admin_views.tests.AdminViewListEditable) ... ok
test_pk_hidden_fields (admin_views.tests.AdminViewListEditable)
hidden pk fields aren't displayed in the table body and their ... ok
test_pk_hidden_fields_with_list_display_links (admin_views.tests.AdminViewListEditable)
Similarly as test_pk_hidden_fields, but when the hidden pk fields are ... ok
test_post_messages (admin_views.tests.AdminViewListEditable) ... ok
test_post_submission (admin_views.tests.AdminViewListEditable) ... ok
test_client_logout_url_can_be_used_to_login (admin_views.tests.AdminViewLogoutTests) ... ok
test_logout (admin_views.tests.AdminViewLogoutTests) ... ok
test_add_view_form_and_formsets_run_validation (admin_views.tests.AdminViewOnSiteTests)
Issue #20522 ... ok
test_callable (admin_views.tests.AdminViewOnSiteTests)
The right link is displayed if view_on_site is a callable ... ok
test_change_view_form_and_formsets_run_validation (admin_views.tests.AdminViewOnSiteTests)
Issue #20522 ... ok
test_check (admin_views.tests.AdminViewOnSiteTests)
The view_on_site value is either a boolean or a callable ... ok
test_false (admin_views.tests.AdminViewOnSiteTests)
The 'View on site' button is not displayed if view_on_site is False ... ok
test_missing_get_absolute_url (admin_views.tests.AdminViewOnSiteTests)
None is returned if model doesn't have get_absolute_url ... ok
test_true (admin_views.tests.AdminViewOnSiteTests)
The default behavior is followed if view_on_site is True ... ok
test_add_view (admin_views.tests.AdminViewPermissionsTest)
Test add view restricts access and actually adds items. ... ok
test_add_view_with_view_only_inlines (admin_views.tests.AdminViewPermissionsTest)
User with add permission to a section but view-only for inlines. ... ok
test_app_list_permissions (admin_views.tests.AdminViewPermissionsTest)
If a user has no module perms, the app list returns a 404. ... ok
test_change_view (admin_views.tests.AdminViewPermissionsTest)
Change view should restrict access and allow users to edit items. ... ok
test_change_view_save_as_new (admin_views.tests.AdminViewPermissionsTest)
'Save as new' should raise PermissionDenied for users without the 'add' ... ok
test_change_view_with_view_and_add_inlines (admin_views.tests.AdminViewPermissionsTest)
User has view and add permissions on the inline model. ... ok
test_change_view_with_view_and_delete_inlines (admin_views.tests.AdminViewPermissionsTest)
User has view and delete permissions on the inline model. ... ok
test_change_view_with_view_only_inlines (admin_views.tests.AdminViewPermissionsTest)
User with change permission to a section but view-only for inlines. ... ok
test_change_view_without_object_change_permission (admin_views.tests.AdminViewPermissionsTest)
The object should be read-only if the user has permission to view it ... ok
test_conditionally_show_add_section_link (admin_views.tests.AdminViewPermissionsTest)
The foreign key widget should only show the "add related" button if the ... ok
test_conditionally_show_change_section_link (admin_views.tests.AdminViewPermissionsTest)
The foreign key widget should only show the "change related" button if ... ok
test_conditionally_show_delete_section_link (admin_views.tests.AdminViewPermissionsTest)
The foreign key widget should only show the "delete related" button if ... ok
test_delete_view (admin_views.tests.AdminViewPermissionsTest)
Delete view should restrict access and actually delete items. ... ok
test_delete_view_nonexistent_obj (admin_views.tests.AdminViewPermissionsTest) ... ok
test_delete_view_with_no_default_permissions (admin_views.tests.AdminViewPermissionsTest)
The delete view allows users to delete collected objects without a ... ok
test_disabled_permissions_when_logged_in (admin_views.tests.AdminViewPermissionsTest) ... ok
test_disabled_staff_permissions_when_logged_in (admin_views.tests.AdminViewPermissionsTest) ... ok
test_double_login_is_not_allowed (admin_views.tests.AdminViewPermissionsTest)
Regression test for #19327 ... ok
test_has_module_permission (admin_views.tests.AdminViewPermissionsTest)
has_module_permission() returns True for all users who ... ok
test_history_view (admin_views.tests.AdminViewPermissionsTest)
History view should restrict access. ... ok
test_history_view_bad_url (admin_views.tests.AdminViewPermissionsTest) ... ok
test_login (admin_views.tests.AdminViewPermissionsTest)
Make sure only staff members can log in. ... ok
test_login_has_permission (admin_views.tests.AdminViewPermissionsTest) ... ok
test_login_page_notice_for_non_staff_users (admin_views.tests.AdminViewPermissionsTest)
A logged-in non-staff user trying to access the admin index should be ... ok
test_login_redirect_for_direct_get (admin_views.tests.AdminViewPermissionsTest)
Login redirect should be to the admin index page when going directly to ... ok
test_login_successfully_redirects_to_original_URL (admin_views.tests.AdminViewPermissionsTest) ... ok
test_overriding_has_module_permission (admin_views.tests.AdminViewPermissionsTest)
If has_module_permission() always returns False, the module shouldn't ... ok
test_post_save_message_no_forbidden_links_visible (admin_views.tests.AdminViewPermissionsTest)
Post-save message shouldn't contain a link to the change form if the ... ok
test_shortcut_view_only_available_to_staff (admin_views.tests.AdminViewPermissionsTest)
Only admin users should be able to use the admin shortcut view. ... ok
test_add (admin_views.tests.AdminViewProxyModelPermissionsTests) ... ok
test_change (admin_views.tests.AdminViewProxyModelPermissionsTests) ... ok
test_delete (admin_views.tests.AdminViewProxyModelPermissionsTests) ... ok
test_view (admin_views.tests.AdminViewProxyModelPermissionsTests) ... ok
test_change_view_history_link (admin_views.tests.AdminViewStringPrimaryKeyTest)
Object history button link should work and contain the pk value quoted. ... ok
test_changelist_to_changeform_link (admin_views.tests.AdminViewStringPrimaryKeyTest)
Link to the changeform of the object in changelist should use reverse() and be quoted -- #18072 ... ok
test_deleteconfirmation_link (admin_views.tests.AdminViewStringPrimaryKeyTest)
The link from the delete confirmation page referring back to the changeform of the object should be quoted ... ok
test_get_change_view (admin_views.tests.AdminViewStringPrimaryKeyTest)
Retrieving the object using urlencoded form of primary key should work ... ok
test_get_history_view (admin_views.tests.AdminViewStringPrimaryKeyTest)
Retrieving the history for an object using urlencoded form of primary ... ok
test_recentactions_link (admin_views.tests.AdminViewStringPrimaryKeyTest)
The link from the recent actions list referring to the changeform of the object should be quoted ... ok
test_redirect_on_add_view_continue_button (admin_views.tests.AdminViewStringPrimaryKeyTest)
As soon as an object is added using "Save and continue editing" ... ok
test_shortcut_view_with_escaping (admin_views.tests.AdminViewStringPrimaryKeyTest)
'View on site should' work properly with char fields ... ok
test_url_conflicts_with_add (admin_views.tests.AdminViewStringPrimaryKeyTest)
A model with a primary key that ends with add or is `add` should be visible ... ok
test_url_conflicts_with_delete (admin_views.tests.AdminViewStringPrimaryKeyTest)
A model with a primary key that ends with delete should be visible ... ok
test_url_conflicts_with_history (admin_views.tests.AdminViewStringPrimaryKeyTest)
A model with a primary key that ends with history should be visible ... ok
test_unicode_delete (admin_views.tests.AdminViewUnicodeTest)
The delete_view handles non-ASCII characters ... ok
test_unicode_edit (admin_views.tests.AdminViewUnicodeTest)
A test to ensure that POST on edit_view handles non-ASCII characters. ... ok
test_no_standard_modeladmin_urls (admin_views.tests.AdminViewsNoUrlTest)
Admin index views don't break when user's ModelAdmin removes standard urls ... ok
test_app_model_in_app_index_body_class (admin_views.tests.CSSTest)
Ensure app and model tag are correctly read by app_index template ... ok
test_app_model_in_delete_confirmation_body_class (admin_views.tests.CSSTest)
Ensure app and model tag are correctly read by delete_confirmation ... ok
test_app_model_in_delete_selected_confirmation_body_class (admin_views.tests.CSSTest)
Ensure app and model tag are correctly read by ... ok
test_app_model_in_form_body_class (admin_views.tests.CSSTest)
Ensure app and model tag are correctly read by change_form template ... ok
test_app_model_in_list_body_class (admin_views.tests.CSSTest)
Ensure app and model tag are correctly read by change_list template ... ok
test_changelist_field_classes (admin_views.tests.CSSTest)
Cells of the change list table should contain the field name in their class attribute ... ok
test_field_prefix_css_classes (admin_views.tests.CSSTest)
Fields have a CSS class name with a 'field-' prefix. ... ok
test_index_css_classes (admin_views.tests.CSSTest)
CSS class names are used for each app and model on the admin index ... ok
test_custom_admin_site_app_index_view_and_template (admin_views.tests.CustomModelAdminTest) ... ok
test_custom_admin_site_index_view_and_template (admin_views.tests.CustomModelAdminTest) ... ok
test_custom_admin_site_login_form (admin_views.tests.CustomModelAdminTest) ... ok
test_custom_admin_site_login_template (admin_views.tests.CustomModelAdminTest) ... ok
test_custom_admin_site_logout_template (admin_views.tests.CustomModelAdminTest) ... ok
test_custom_admin_site_password_change_done_template (admin_views.tests.CustomModelAdminTest) ... ok
test_custom_admin_site_password_change_template (admin_views.tests.CustomModelAdminTest) ... ok
test_custom_admin_site_password_change_with_extra_context (admin_views.tests.CustomModelAdminTest) ... ok
test_custom_admin_site_view (admin_views.tests.CustomModelAdminTest) ... ok
test_pwd_change_custom_template (admin_views.tests.CustomModelAdminTest) ... ok
test_empty (admin_views.tests.DateHierarchyTests)
No date hierarchy links display with empty changelist. ... ok
test_multiple_years (admin_views.tests.DateHierarchyTests)
year-level links appear for year-spanning changelist. ... ok
test_related_field (admin_views.tests.DateHierarchyTests) ... ok
test_single (admin_views.tests.DateHierarchyTests)
Single day-level date hierarchy appears for single object. ... ok
test_within_month (admin_views.tests.DateHierarchyTests)
day-level links appear for changelist within single month. ... ok
test_within_year (admin_views.tests.DateHierarchyTests)
month-level links appear for changelist within single year. ... ok
test_explicitly_provided_pk (admin_views.tests.GetFormsetsWithInlinesArgumentTest) ... ok
test_implicitly_generated_pk (admin_views.tests.GetFormsetsWithInlinesArgumentTest) ... ok
test_group_permission_performance (admin_views.tests.GroupAdminTest) ... ok
test_save_button (admin_views.tests.GroupAdminTest) ... ok
test_callable (admin_views.tests.InlineAdminViewOnSiteTest)
The right link is displayed if view_on_site is a callable ... ok
test_false (admin_views.tests.InlineAdminViewOnSiteTest)
The 'View on site' button is not displayed if view_on_site is False ... ok
test_true (admin_views.tests.InlineAdminViewOnSiteTest)
The 'View on site' button is displayed if view_on_site is True ... ok
test_limit_choices_to_as_callable (admin_views.tests.LimitChoicesToInAdminTest)
Test for ticket 2445 changes to admin. ... ok
test_add_view (admin_views.tests.NamespacedAdminKeepChangeListFiltersTests) ... ok
test_add_view_without_preserved_filters (admin_views.tests.NamespacedAdminKeepChangeListFiltersTests) ... ok
test_assert_url_equal (admin_views.tests.NamespacedAdminKeepChangeListFiltersTests) ... ok
test_change_view (admin_views.tests.NamespacedAdminKeepChangeListFiltersTests) ... ok
test_change_view_without_preserved_filters (admin_views.tests.NamespacedAdminKeepChangeListFiltersTests) ... ok
test_changelist_view (admin_views.tests.NamespacedAdminKeepChangeListFiltersTests) ... ok
test_delete_view (admin_views.tests.NamespacedAdminKeepChangeListFiltersTests) ... ok
test_url_prefix (admin_views.tests.NamespacedAdminKeepChangeListFiltersTests) ... ok
test_JS_i18n (admin_views.tests.NeverCacheTests)
Check the never-cache status of the JavaScript i18n view ... ok
test_admin_index (admin_views.tests.NeverCacheTests)
Check the never-cache status of the main index ... ok
test_app_index (admin_views.tests.NeverCacheTests)
Check the never-cache status of an application index ... ok
test_login (admin_views.tests.NeverCacheTests)
Check the never-cache status of login views ... ok
test_logout (admin_views.tests.NeverCacheTests)
Check the never-cache status of logout view ... ok
test_model_add (admin_views.tests.NeverCacheTests)
Check the never-cache status of a model add page ... ok
test_model_delete (admin_views.tests.NeverCacheTests)
Check the never-cache status of a model delete page ... ok
test_model_history (admin_views.tests.NeverCacheTests)
Check the never-cache status of a model history page ... ok
test_model_index (admin_views.tests.NeverCacheTests)
Check the never-cache status of a model index ... ok
test_model_view (admin_views.tests.NeverCacheTests)
Check the never-cache status of a model edit page ... ok
test_password_change (admin_views.tests.NeverCacheTests)
Check the never-cache status of the password change view ... ok
test_password_change_done (admin_views.tests.NeverCacheTests)
Check the never-cache status of the password change done view ... ok
test_prepopulated_maxlength_localized (admin_views.tests.PrePopulatedTest)
Regression test for #15938: if USE_THOUSAND_SEPARATOR is set, make sure ... ok
test_prepopulated_off (admin_views.tests.PrePopulatedTest) ... ok
test_prepopulated_on (admin_views.tests.PrePopulatedTest) ... ok
test_view_only_add_form (admin_views.tests.PrePopulatedTest)
PrePopulatedPostReadOnlyAdmin.prepopulated_fields includes 'slug' ... ok
test_view_only_change_form (admin_views.tests.PrePopulatedTest)
PrePopulatedPostReadOnlyAdmin.prepopulated_fields includes 'slug'. That ... ok
test_limit_choices_to (admin_views.tests.RawIdFieldsTest)
Regression test for 14880 ... ok
test_limit_choices_to_isnull_false (admin_views.tests.RawIdFieldsTest)
Regression test for 20182 ... ok
test_limit_choices_to_isnull_true (admin_views.tests.RawIdFieldsTest)
Regression test for 20182 ... ok
test_list_display_method_same_name_as_reverse_accessor (admin_views.tests.RawIdFieldsTest)
Should be able to use a ModelAdmin method in list_display that has the ... ok
test_change_form_renders_correct_null_choice_value (admin_views.tests.ReadonlyTest)
Regression test for #17911. ... ok
test_correct_autoescaping (admin_views.tests.ReadonlyTest)
Make sure that non-field readonly elements are properly autoescaped (#24461) ... ok
test_label_suffix_translated (admin_views.tests.ReadonlyTest) ... ok
test_readonly_field_overrides (admin_views.tests.ReadonlyTest)
Regression test for #22087 - ModelForm Meta overrides are ignored by ... ok
test_readonly_foreignkey_links (admin_views.tests.ReadonlyTest)
ForeignKey readonly fields render as links if the target model is ... ok
test_readonly_get (admin_views.tests.ReadonlyTest) ... ok
test_readonly_manytomany (admin_views.tests.ReadonlyTest)
Regression test for #13004 ... ok
test_readonly_manytomany_backwards_ref (admin_views.tests.ReadonlyTest)
Regression test for #16433 - backwards references for related objects ... ok
test_readonly_manytomany_forwards_ref (admin_views.tests.ReadonlyTest) ... ok
test_readonly_onetoone_backwards_ref (admin_views.tests.ReadonlyTest)
Can reference a reverse OneToOneField in ModelAdmin.readonly_fields. ... ok
test_readonly_post (admin_views.tests.ReadonlyTest) ... ok
test_readonly_text_field (admin_views.tests.ReadonlyTest) ... ok
test_user_password_change_limited_queryset (admin_views.tests.ReadonlyTest) ... ok
test_save_as_continue_false (admin_views.tests.SaveAsTests)
Saving a new object using "Save as new" redirects to the changelist ... ok
test_save_as_duplication (admin_views.tests.SaveAsTests)
'save as' creates a new person ... ok
test_save_as_new_with_inlines_with_validation_errors (admin_views.tests.SaveAsTests) ... ok
test_save_as_new_with_validation_errors (admin_views.tests.SaveAsTests)
When you click "Save as new" and have a validation error, ... ok
test_save_as_new_with_validation_errors_with_inlines (admin_views.tests.SaveAsTests) ... ok
test_secure_view_shows_login_if_not_logged_in (admin_views.tests.SecureViewTests) ... ok
test_staff_member_required_decorator_works_with_argument (admin_views.tests.SecureViewTests)
Staff_member_required decorator works with an argument ... ok
test_custom_changelist (admin_views.tests.TestCustomChangeList)
Validate that a custom ChangeList class can be used (#9749) ... ok
test_generic_content_object_in_list_display (admin_views.tests.TestGenericRelations) ... ok
test_GET_parent_add (admin_views.tests.TestInlineNotEditable)
InlineModelAdmin broken? ... ok
test_all_fields_hidden (admin_views.tests.TestLabelVisibility) ... ok
test_all_fields_visible (admin_views.tests.TestLabelVisibility) ... ok
test_mixin (admin_views.tests.TestLabelVisibility) ... ok
test_form_url_present_in_context (admin_views.tests.UserAdminTest) ... ok
test_password_mismatch (admin_views.tests.UserAdminTest) ... ok
test_save_add_another_button (admin_views.tests.UserAdminTest) ... ok
test_save_button (admin_views.tests.UserAdminTest) ... ok
test_save_continue_editing_button (admin_views.tests.UserAdminTest) ... ok
test_user_fk_add_popup (admin_views.tests.UserAdminTest)
User addition through a FK popup should return the appropriate JavaScript response. ... ok
test_user_fk_change_popup (admin_views.tests.UserAdminTest)
User change through a FK popup should return the appropriate JavaScript response. ... ok
test_user_fk_delete_popup (admin_views.tests.UserAdminTest)
User deletion through a FK popup should return the appropriate JavaScript response. ... ok
test_user_permission_performance (admin_views.tests.UserAdminTest) ... ok
test_lang_name_present (admin_views.tests.ValidXHTMLTests) ... ok
test_cancel_delete_confirmation (admin_views.tests.SeleniumTests)
Cancelling the deletion of an object takes the user back one page. ... skipped 'No browsers specified.'
test_cancel_delete_related_confirmation (admin_views.tests.SeleniumTests)
Cancelling the deletion of an object with relations takes the user back ... skipped 'No browsers specified.'
test_collapsible_fieldset (admin_views.tests.SeleniumTests)
The 'collapse' class in fieldsets definition allows to ... skipped 'No browsers specified.'
test_first_field_focus (admin_views.tests.SeleniumTests)
JavaScript-assisted auto-focus on first usable form field. ... skipped 'No browsers specified.'
test_inline_uuid_pk_add_with_popup (admin_views.tests.SeleniumTests) ... skipped 'No browsers specified.'
test_inline_uuid_pk_delete_with_popup (admin_views.tests.SeleniumTests) ... skipped 'No browsers specified.'
test_inline_uuid_pk_edit_with_popup (admin_views.tests.SeleniumTests) ... skipped 'No browsers specified.'
test_inline_with_popup_cancel_delete (admin_views.tests.SeleniumTests)
Clicking ""No, take me back" on a delete popup closes the window. ... skipped 'No browsers specified.'
test_input_element_font (admin_views.tests.SeleniumTests)
Browsers' default stylesheets override the font of inputs. The admin ... skipped 'No browsers specified.'
test_list_editable_popups (admin_views.tests.SeleniumTests)
list_editable foreign keys have add/change popups. ... skipped 'No browsers specified.'
test_list_editable_raw_id_fields (admin_views.tests.SeleniumTests) ... skipped 'No browsers specified.'
test_login_button_centered (admin_views.tests.SeleniumTests) ... skipped 'No browsers specified.'
test_populate_existing_object (admin_views.tests.SeleniumTests)
The prepopulation works for existing objects too, as long as ... skipped 'No browsers specified.'
test_prepopulated_fields (admin_views.tests.SeleniumTests)
The JavaScript-automated prepopulated fields work with the main form ... skipped 'No browsers specified.'
test_search_input_filtered_page (admin_views.tests.SeleniumTests) ... skipped 'No browsers specified.'

----------------------------------------------------------------------
Ran 344 tests in 34.586s

OK (skipped=15)
Destroying test database for alias 'default' ('file:memorydb_default?mode=memory&cache=shared')...
