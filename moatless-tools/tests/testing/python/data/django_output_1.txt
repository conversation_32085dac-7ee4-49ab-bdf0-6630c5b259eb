>>>>> Run tests
Creating test database for alias 'default' ('file:memorydb_default?mode=memory&cache=shared')...
test_get_choices (model_fields.tests.GetChoicesLimitChoicesToTests) ... ok
test_get_choices_reverse_related_field (model_fields.tests.GetChoicesLimitChoicesToTests) ... ok
test_get_choices (model_fields.tests.GetChoicesOrderingTests) ... ok
test_get_choices_default_ordering (model_fields.tests.GetChoicesOrderingTests) ... ok
test_get_choices_reverse_related_field (model_fields.tests.GetChoicesOrderingTests) ... ok
test_get_choices_reverse_related_field_default_ordering (model_fields.tests.GetChoicesOrderingTests) ... ok
test_choices_form_class (model_fields.tests.BasicFieldTests)
Can supply a custom choices form class to Field.formfield() ... ok
test_deconstruct_nested_field (model_fields.tests.BasicFieldTests)
deconstruct() uses __qualname__ for nested class support. ... ok
test_field_instance_is_picklable (model_fields.tests.BasicFieldTests)
Field instances can be pickled. ... ok
test_field_name (model_fields.tests.BasicFieldTests) ... ok
test_field_ordering (model_fields.tests.BasicFieldTests)
Fields are ordered based on their creation. ... ok
test_field_repr (model_fields.tests.BasicFieldTests) ... ok
test_field_repr_nested (model_fields.tests.BasicFieldTests)
__repr__() uses __qualname__ for nested class support. ... ok
test_field_str (model_fields.tests.BasicFieldTests) ... ok
test_field_verbose_name (model_fields.tests.BasicFieldTests) ... ok
test_formfield_disabled (model_fields.tests.BasicFieldTests)
Field.formfield() sets disabled for fields with choices. ... ok
test_show_hidden_initial (model_fields.tests.BasicFieldTests) ... ok
test_check (model_fields.tests.ChoicesTests) ... ok
test_choices (model_fields.tests.ChoicesTests) ... ok
test_flatchoices (model_fields.tests.ChoicesTests) ... ok
test_formfield (model_fields.tests.ChoicesTests) ... ok
test_invalid_choice (model_fields.tests.ChoicesTests) ... ok
test_blank_in_choices (model_fields.tests.GetChoicesTests) ... ok
test_blank_in_grouped_choices (model_fields.tests.GetChoicesTests) ... ok
test_empty_choices (model_fields.tests.GetChoicesTests) ... ok
test_lazy_strings_not_evaluated (model_fields.tests.GetChoicesTests) ... ok
test_choices_and_field_display (model_fields.tests.GetFieldDisplayTests) ... ERROR
test_empty_iterator_choices (model_fields.tests.GetFieldDisplayTests) ... ok
test_get_FIELD_display_translated (model_fields.tests.GetFieldDisplayTests)
A translated display value is coerced to str. ... Testing against Django installed in '/testbed/django'
Importing application model_fields
Skipping setup of unused database(s): other.
Operations to perform:
  Synchronize unmigrated apps: auth, contenttypes, messages, model_fields, sessions, staticfiles
  Apply all migrations: admin, sites
Synchronizing apps without migrations:
  Creating tables...
    Creating table django_content_type
    Creating table auth_permission
    Creating table auth_group
    Creating table auth_user
    Creating table django_session
    Creating table model_fields_foo
    Creating table model_fields_bar
    Creating table model_fields_whiz
    Creating table model_fields_whizdelayed
    Creating table model_fields_whiziter
    Creating table model_fields_whiziterempty
    Creating table model_fields_choiceful
    Creating table model_fields_bigd
    Creating table model_fields_floatmodel
    Creating table model_fields_bigs
    Creating table model_fields_unicodeslugfield
    Creating table model_fields_automodel
    Creating table model_fields_bigautomodel
    Creating table model_fields_smallautomodel
    Creating table model_fields_smallintegermodel
    Creating table model_fields_integermodel
    Creating table model_fields_bigintegermodel
    Creating table model_fields_positivesmallintegermodel
    Creating table model_fields_positiveintegermodel
    Creating table model_fields_post
    Creating table model_fields_nullbooleanmodel
    Creating table model_fields_booleanmodel
    Creating table model_fields_datetimemodel
    Creating table model_fields_durationmodel
    Creating table model_fields_nulldurationmodel
    Creating table model_fields_primarykeycharmodel
    Creating table model_fields_fkstobooleans
    Creating table model_fields_fktochar
    Creating table model_fields_renamedfield
    Creating table model_fields_verbosenamefield
    Creating table model_fields_genericipaddress
    Creating table model_fields_decimallessthanone
    Creating table model_fields_fieldclassattributemodel
    Creating table model_fields_datamodel
    Creating table model_fields_document
    Creating table model_fields_person
    Creating table model_fields_personwithheight
    Creating table model_fields_personwithheightandwidth
    Creating table model_fields_persondimensionsfirst
    Creating table model_fields_persontwoimages
    Creating table model_fields_allfieldsmodel
    Creating table model_fields_manytomany
    Creating table model_fields_uuidmodel
    Creating table model_fields_nullableuuidmodel
    Creating table model_fields_primarykeyuuidmodel
    Creating table model_fields_relatedtouuidmodel
    Creating table model_fields_uuidchild
    Creating table model_fields_uuidgrandchild
    Running deferred SQL...
Running migrations:
  Applying admin.0001_initial... OK
  Applying admin.0002_logentry_remove_auto_add... OK
  Applying admin.0003_logentry_add_action_flag_choices... OK
  Applying sites.0001_initial... OK
  Applying sites.0002_alter_domain_unique... OK
System check identified no issues (0 silenced).
ERROR
test_iterator_choices (model_fields.tests.GetFieldDisplayTests) ... ok

======================================================================
ERROR: test_choices_and_field_display (model_fields.tests.GetFieldDisplayTests)
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/testbed/tests/model_fields/tests.py", line 158, in test_choices_and_field_display
    self.assertEqual(Whiz(c=1).get_c_display(), 'First')    # A nested value
  File "/testbed/django/db/models/base.py", line 944, in _get_FIELD_display
    return getattr(self, method_name)()
  File "/testbed/django/db/models/base.py", line 944, in _get_FIELD_display
    return getattr(self, method_name)()
  File "/testbed/django/db/models/base.py", line 944, in _get_FIELD_display
    return getattr(self, method_name)()
  [Previous line repeated 240 more times]
  File "/testbed/django/db/models/base.py", line 943, in _get_FIELD_display
    if hasattr(self, method_name):
  File "/opt/miniconda3/envs/testbed/lib/python3.6/functools.py", line 379, in __get__
    new_func = get(obj, cls)
RecursionError: maximum recursion depth exceeded while calling a Python object

======================================================================
ERROR: test_get_FIELD_display_translated (model_fields.tests.GetFieldDisplayTests)
A translated display value is coerced to str.
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/testbed/tests/model_fields/tests.py", line 167, in test_get_FIELD_display_translated
    val = Whiz(c=5).get_c_display()
  File "/testbed/django/db/models/base.py", line 944, in _get_FIELD_display
    return getattr(self, method_name)()
  File "/testbed/django/db/models/base.py", line 944, in _get_FIELD_display
    return getattr(self, method_name)()
  File "/testbed/django/db/models/base.py", line 944, in _get_FIELD_display
    return getattr(self, method_name)()
  [Previous line repeated 240 more times]
  File "/testbed/django/db/models/base.py", line 943, in _get_FIELD_display
    if hasattr(self, method_name):
  File "/opt/miniconda3/envs/testbed/lib/python3.6/functools.py", line 379, in __get__
    new_func = get(obj, cls)
RecursionError: maximum recursion depth exceeded while calling a Python object

----------------------------------------------------------------------
Ran 30 tests in 0.076s
