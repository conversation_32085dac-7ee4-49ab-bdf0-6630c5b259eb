"Updated 0 paths from 0416a4fc1
Checking patch testing/logging/test_reporting.py...
Applied patch testing/logging/test_reporting.py cleanly.
>>>>> Run tests
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /testbed, inifile: tox.ini
collected 34 items

testing/logging/test_reporting.py ..................................     [100%]

==================================== PASSES ====================================
___________________ test_live_logging_suspends_capture[True] ___________________
------------------------------ Captured log call -------------------------------
CRITICAL test_reporting.test_live_logging_suspends_capture:test_reporting.py:905 some message
__________________ test_live_logging_suspends_capture[False] ___________________
------------------------------ Captured log call -------------------------------
CRITICAL test_reporting.test_live_logging_suspends_capture:test_reporting.py:905 some message
_____________________________ test_nothing_logged ______________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_nothing_logged0
collected 1 item

test_nothing_logged.py F                                                 [100%]

=================================== FAILURES ===================================
___________________________________ test_foo ___________________________________

    def test_foo():
        sys.stdout.write('text going to stdout')
        sys.stderr.write('text going to stderr')
>       assert False
E       assert False

test_nothing_logged.py:6: AssertionError
----------------------------- Captured stdout call -----------------------------
text going to stdout
----------------------------- Captured stderr call -----------------------------
text going to stderr
=========================== 1 failed in 0.04 seconds ===========================
_____________________________ test_messages_logged _____________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_messages_logged0
collected 1 item

test_messages_logged.py F                                                [100%]

=================================== FAILURES ===================================
___________________________________ test_foo ___________________________________

    def test_foo():
        sys.stdout.write('text going to stdout')
        sys.stderr.write('text going to stderr')
        logger.info('text going to logger')
>       assert False
E       assert False

test_messages_logged.py:10: AssertionError
----------------------------- Captured stdout call -----------------------------
text going to stdout
----------------------------- Captured stderr call -----------------------------
text going to stderr
------------------------------ Captured log call -------------------------------
INFO     test_messages_logged:test_messages_logged.py:9 text going to logger
=========================== 1 failed in 0.02 seconds ===========================
------------------------------ Captured log call -------------------------------
INFO     test_messages_logged:test_messages_logged.py:9 text going to logger
__________________________ test_root_logger_affected ___________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_root_logger_affected0
collected 1 item

test_root_logger_affected.py F                                           [100%]

=================================== FAILURES ===================================
___________________________________ test_foo ___________________________________

    def test_foo():
        logger.info('info text ' + 'going to logger')
        logger.warning('warning text ' + 'going to logger')
        logger.error('error text ' + 'going to logger')

>       assert 0
E       assert 0

test_root_logger_affected.py:9: AssertionError
------------------------------ Captured log call -------------------------------
ERROR    root:test_root_logger_affected.py:7 error text going to logger
=========================== 1 failed in 0.02 seconds ===========================
------------------------------ Captured log call -------------------------------
WARNING  root:test_root_logger_affected.py:6 warning text going to logger
ERROR    root:test_root_logger_affected.py:7 error text going to logger
___________________ test_log_cli_level_log_level_interaction ___________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-8/test_log_cli_level_log_level_interaction0
collecting ... collected 1 item

test_log_cli_level_log_level_interaction.py::test_foo
-------------------------------- live log call ---------------------------------
INFO     root:test_log_cli_level_log_level_interaction.py:6 info text going to logger
WARNING  root:test_log_cli_level_log_level_interaction.py:7 warning text going to logger
ERROR    root:test_log_cli_level_log_level_interaction.py:8 error text going to logger
FAILED                                                                   [100%]

=================================== FAILURES ===================================
___________________________________ test_foo ___________________________________

    def test_foo():
        logger.debug('debug text ' + 'going to logger')
        logger.info('info text ' + 'going to logger')
        logger.warning('warning text ' + 'going to logger')
        logger.error('error text ' + 'going to logger')
>       assert 0
E       assert 0

test_log_cli_level_log_level_interaction.py:9: AssertionError
------------------------------ Captured log call -------------------------------
ERROR    root:test_log_cli_level_log_level_interaction.py:8 error text going to logger
=========================== 1 failed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
INFO     root:test_log_cli_level_log_level_interaction.py:6 info text going to logger
WARNING  root:test_log_cli_level_log_level_interaction.py:7 warning text going to logger
ERROR    root:test_log_cli_level_log_level_interaction.py:8 error text going to logger
______________________________ test_setup_logging ______________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_setup_logging0
collected 1 item

test_setup_logging.py F                                                  [100%]

=================================== FAILURES ===================================
___________________________________ test_foo ___________________________________

    def test_foo():
        logger.info('text going to logger from call')
>       assert False
E       assert False

test_setup_logging.py:10: AssertionError
------------------------------ Captured log setup ------------------------------
INFO     test_setup_logging:test_setup_logging.py:6 text going to logger from setup
------------------------------ Captured log call -------------------------------
INFO     test_setup_logging:test_setup_logging.py:9 text going to logger from call
=========================== 1 failed in 0.02 seconds ===========================
------------------------------ Captured log call -------------------------------
INFO     test_setup_logging:test_setup_logging.py:6 text going to logger from setup
INFO     test_setup_logging:test_setup_logging.py:9 text going to logger from call
____________________________ test_teardown_logging _____________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_teardown_logging0
collected 1 item

test_teardown_logging.py .E                                              [100%]

==================================== ERRORS ====================================
________________________ ERROR at teardown of test_foo _________________________

function = <function test_foo at 0x7f3539c973a0>

    def teardown_function(function):
        logger.info('text going to logger from teardown')
>       assert False
E       assert False

test_teardown_logging.py:10: AssertionError
------------------------------ Captured log call -------------------------------
INFO     test_teardown_logging:test_teardown_logging.py:6 text going to logger from call
---------------------------- Captured log teardown -----------------------------
INFO     test_teardown_logging:test_teardown_logging.py:9 text going to logger from teardown
====================== 1 passed, 1 error in 0.02 seconds =======================
------------------------------ Captured log call -------------------------------
INFO     test_teardown_logging:test_teardown_logging.py:6 text going to logger from call
INFO     test_teardown_logging:test_teardown_logging.py:9 text going to logger from teardown
__________________________ test_disable_log_capturing __________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_disable_log_capturing0
collected 1 item

test_disable_log_capturing.py F                                          [100%]

=================================== FAILURES ===================================
___________________________________ test_foo ___________________________________

    def test_foo():
        sys.stdout.write('text going to stdout')
        logger.warning('catch me if you can!')
        sys.stderr.write('text going to stderr')
>       assert False
E       assert False

test_disable_log_capturing.py:10: AssertionError
----------------------------- Captured stdout call -----------------------------
text going to stdout
----------------------------- Captured stderr call -----------------------------
text going to stderr
=========================== 1 failed in 0.01 seconds ===========================
<_pytest.pytester.LineMatcher object at 0x7f3539ceffd0>
------------------------------ Captured log call -------------------------------
WARNING  test_disable_log_capturing:test_disable_log_capturing.py:8 catch me if you can!
________________________ test_disable_log_capturing_ini ________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_disable_log_capturing_ini0, inifile: tox.ini
collected 1 item

test_disable_log_capturing_ini.py F                                      [100%]

=================================== FAILURES ===================================
___________________________________ test_foo ___________________________________

    def test_foo():
        sys.stdout.write('text going to stdout')
        logger.warning('catch me if you can!')
        sys.stderr.write('text going to stderr')
>       assert False
E       assert False

test_disable_log_capturing_ini.py:10: AssertionError
----------------------------- Captured stdout call -----------------------------
text going to stdout
----------------------------- Captured stderr call -----------------------------
text going to stderr
=========================== 1 failed in 0.02 seconds ===========================
<_pytest.pytester.LineMatcher object at 0x7f3539c19520>
------------------------------ Captured log call -------------------------------
WARNING  test_disable_log_capturing_ini:test_disable_log_capturing_ini.py:8 catch me if you can!
_____________________ test_log_cli_enabled_disabled[True] ______________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-8/test_log_cli_enabled_disabled0, inifile: tox.ini
collecting ... collected 1 item

test_log_cli_enabled_disabled.py::test_log_cli
-------------------------------- live log call ---------------------------------
CRITICAL root:test_log_cli_enabled_disabled.py:3 critical message logged by test
PASSED                                                                   [100%]

=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
CRITICAL root:test_log_cli_enabled_disabled.py:3 critical message logged by test
_____________________ test_log_cli_enabled_disabled[False] _____________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_log_cli_enabled_disabled1
collected 1 item

test_log_cli_enabled_disabled.py .                                       [100%]

=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
CRITICAL root:test_log_cli_enabled_disabled.py:3 critical message logged by test
__________________________ test_log_cli_default_level __________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-8/test_log_cli_default_level0, inifile: tox.ini
collecting ... collected 1 item

test_log_cli_default_level.py::test_log_cli
-------------------------------- live log call ---------------------------------
WARNING  catchlog:test_log_cli_default_level.py:7 WARNING message will be shown
PASSED                                                                   [100%]

=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
WARNING  catchlog:test_log_cli_default_level.py:7 WARNING message will be shown
__________________ test_log_cli_default_level_multiple_tests ___________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-8/test_log_cli_default_level_multiple_tests0, inifile: tox.ini
collecting ... collected 2 items

test_log_cli_default_level_multiple_tests.py::test_log_1
-------------------------------- live log call ---------------------------------
WARNING  root:test_log_cli_default_level_multiple_tests.py:4 log message from test_log_1
PASSED                                                                   [ 50%]
test_log_cli_default_level_multiple_tests.py::test_log_2
-------------------------------- live log call ---------------------------------
WARNING  root:test_log_cli_default_level_multiple_tests.py:7 log message from test_log_2
PASSED                                                                   [100%]

=========================== 2 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
WARNING  root:test_log_cli_default_level_multiple_tests.py:4 log message from test_log_1
WARNING  root:test_log_cli_default_level_multiple_tests.py:7 log message from test_log_2
_____________________ test_log_cli_default_level_sections ______________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-8/test_log_cli_default_level_sections0, inifile: tox.ini
collecting ... collected 2 items

test_log_cli_default_level_sections.py::test_log_1
-------------------------------- live log start --------------------------------
WARNING  root:conftest.py:5 >>>>> START >>>>>
-------------------------------- live log setup --------------------------------
WARNING  root:test_log_cli_default_level_sections.py:6 log message from setup of test_log_1
-------------------------------- live log call ---------------------------------
WARNING  root:test_log_cli_default_level_sections.py:11 log message from test_log_1
PASSED                                                                   [ 50%]
------------------------------ live log teardown -------------------------------
WARNING  root:test_log_cli_default_level_sections.py:8 log message from teardown of test_log_1
------------------------------- live log finish --------------------------------
WARNING  root:conftest.py:8 <<<<< END <<<<<<<

test_log_cli_default_level_sections.py::test_log_2
-------------------------------- live log start --------------------------------
WARNING  root:conftest.py:5 >>>>> START >>>>>
-------------------------------- live log setup --------------------------------
WARNING  root:test_log_cli_default_level_sections.py:6 log message from setup of test_log_2
-------------------------------- live log call ---------------------------------
WARNING  root:test_log_cli_default_level_sections.py:14 log message from test_log_2
PASSED                                                                   [100%]
------------------------------ live log teardown -------------------------------
WARNING  root:test_log_cli_default_level_sections.py:8 log message from teardown of test_log_2
------------------------------- live log finish --------------------------------
WARNING  root:conftest.py:8 <<<<< END <<<<<<<


=========================== 2 passed in 0.02 seconds ===========================
------------------------------ Captured log call -------------------------------
WARNING  root:conftest.py:5 >>>>> START >>>>>
WARNING  root:test_log_cli_default_level_sections.py:6 log message from setup of test_log_1
WARNING  root:test_log_cli_default_level_sections.py:11 log message from test_log_1
WARNING  root:test_log_cli_default_level_sections.py:8 log message from teardown of test_log_1
WARNING  root:conftest.py:8 <<<<< END <<<<<<<
WARNING  root:conftest.py:5 >>>>> START >>>>>
WARNING  root:test_log_cli_default_level_sections.py:6 log message from setup of test_log_2
WARNING  root:test_log_cli_default_level_sections.py:14 log message from test_log_2
WARNING  root:test_log_cli_default_level_sections.py:8 log message from teardown of test_log_2
WARNING  root:conftest.py:8 <<<<< END <<<<<<<
_______________________ test_live_logs_unknown_sections ________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-8/test_live_logs_unknown_sections0, inifile: tox.ini
collecting ... collected 1 item

----------------------------- live log collection ------------------------------
WARNING  root:conftest.py:5 Unknown Section!

test_live_logs_unknown_sections.py::test_log_1
-------------------------------- live log start --------------------------------
WARNING  root:conftest.py:8 >>>>> START >>>>>
-------------------------------- live log setup --------------------------------
WARNING  root:test_live_logs_unknown_sections.py:6 log message from setup of test_log_1
-------------------------------- live log call ---------------------------------
WARNING  root:test_live_logs_unknown_sections.py:11 log message from test_log_1
PASSED                                                                   [100%]
------------------------------ live log teardown -------------------------------
WARNING  root:test_live_logs_unknown_sections.py:8 log message from teardown of test_log_1
------------------------------- live log finish --------------------------------
WARNING  root:conftest.py:11 <<<<< END <<<<<<<


=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
WARNING  root:conftest.py:5 Unknown Section!
WARNING  root:conftest.py:8 >>>>> START >>>>>
WARNING  root:test_live_logs_unknown_sections.py:6 log message from setup of test_log_1
WARNING  root:test_live_logs_unknown_sections.py:11 log message from test_log_1
WARNING  root:test_live_logs_unknown_sections.py:8 log message from teardown of test_log_1
WARNING  root:conftest.py:11 <<<<< END <<<<<<<
_______________ test_sections_single_new_line_after_test_outcome _______________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-8/test_sections_single_new_line_after_test_outcome0, inifile: tox.ini
collecting ... collected 1 item

test_sections_single_new_line_after_test_outcome.py::test_log_1
-------------------------------- live log start --------------------------------
WARNING  root:conftest.py:5 >>>>> START >>>>>
-------------------------------- live log setup --------------------------------
WARNING  root:test_sections_single_new_line_after_test_outcome.py:6 log message from setup of test_log_1
-------------------------------- live log call ---------------------------------
WARNING  root:test_sections_single_new_line_after_test_outcome.py:12 log message from test_log_1
PASSED                                                                   [100%]
------------------------------ live log teardown -------------------------------
WARNING  root:test_sections_single_new_line_after_test_outcome.py:8 log message from teardown of test_log_1
WARNING  root:test_sections_single_new_line_after_test_outcome.py:9 log message from teardown of test_log_1
------------------------------- live log finish --------------------------------
WARNING  root:conftest.py:8 <<<<< END <<<<<<<
WARNING  root:conftest.py:9 <<<<< END <<<<<<<


=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
WARNING  root:conftest.py:5 >>>>> START >>>>>
WARNING  root:test_sections_single_new_line_after_test_outcome.py:6 log message from setup of test_log_1
WARNING  root:test_sections_single_new_line_after_test_outcome.py:12 log message from test_log_1
WARNING  root:test_sections_single_new_line_after_test_outcome.py:8 log message from teardown of test_log_1
WARNING  root:test_sections_single_new_line_after_test_outcome.py:9 log message from teardown of test_log_1
WARNING  root:conftest.py:8 <<<<< END <<<<<<<
WARNING  root:conftest.py:9 <<<<< END <<<<<<<
______________________________ test_log_cli_level ______________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-8/test_log_cli_level0, inifile: tox.ini
collecting ... collected 1 item

test_log_cli_level.py::test_log_cli
-------------------------------- live log call ---------------------------------
INFO     catchlog:test_log_cli_level.py:7 This log message will be shown
PASSED
PASSED

=========================== 1 passed in 0.01 seconds ===========================
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-8/test_log_cli_level0, inifile: tox.ini
collecting ... collected 1 item

test_log_cli_level.py::test_log_cli
-------------------------------- live log call ---------------------------------
INFO     catchlog:test_log_cli_level.py:7 This log message will be shown
PASSED
PASSED

=========================== 1 passed in 0.00 seconds ===========================
------------------------------ Captured log call -------------------------------
INFO     catchlog:test_log_cli_level.py:7 This log message will be shown
INFO     catchlog:test_log_cli_level.py:7 This log message will be shown
____________________________ test_log_cli_ini_level ____________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-8/test_log_cli_ini_level0, inifile: tox.ini
collecting ... collected 1 item

test_log_cli_ini_level.py::test_log_cli
-------------------------------- live log call ---------------------------------
INFO     catchlog:test_log_cli_ini_level.py:7 This log message will be shown
PASSED
PASSED

=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
INFO     catchlog:test_log_cli_ini_level.py:7 This log message will be shown
__________________________ test_log_cli_auto_enable[] __________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_log_cli_auto_enable0, inifile: tox.ini
collected 1 item

test_log_cli_auto_enable.py .                                            [100%]

=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
INFO     root:test_log_cli_auto_enable.py:4 log message from test_log_1 not to be shown
WARNING  root:test_log_cli_auto_enable.py:5 log message from test_log_1
________________ test_log_cli_auto_enable[--log-level=WARNING] _________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_log_cli_auto_enable1, inifile: tox.ini
collected 1 item

test_log_cli_auto_enable.py .                                            [100%]

=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
WARNING  root:test_log_cli_auto_enable.py:5 log message from test_log_1
______________ test_log_cli_auto_enable[--log-file-level=WARNING] ______________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_log_cli_auto_enable2, inifile: tox.ini
collected 1 item

test_log_cli_auto_enable.py .                                            [100%]

=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
INFO     root:test_log_cli_auto_enable.py:4 log message from test_log_1 not to be shown
WARNING  root:test_log_cli_auto_enable.py:5 log message from test_log_1
______________ test_log_cli_auto_enable[--log-cli-level=WARNING] _______________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-8/test_log_cli_auto_enable3, inifile: tox.ini
collecting ... collected 1 item

test_log_cli_auto_enable.py::test_log_1
-------------------------------- live log call ---------------------------------
WARNING  root:test_log_cli_auto_enable.py:5 log message from test_log_1
PASSED                                                                   [100%]

=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
INFO     root:test_log_cli_auto_enable.py:4 log message from test_log_1 not to be shown
WARNING  root:test_log_cli_auto_enable.py:5 log message from test_log_1
______________________________ test_log_file_cli _______________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_log_file_cli0
collected 1 item

test_log_file_cli.py PASSED
.

=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
WARNING  catchlog:test_log_file_cli.py:7 This log message will be shown
___________________________ test_log_file_cli_level ____________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_log_file_cli_level0
collected 1 item

test_log_file_cli_level.py PASSED
.

=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
INFO     catchlog:test_log_file_cli_level.py:7 This log message will be shown
____________________ test_log_level_not_changed_by_default _____________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_log_level_not_changed_by_default0
collected 1 item

test_log_level_not_changed_by_default.py .

=========================== 1 passed in 0.01 seconds ===========================
______________________________ test_log_file_ini _______________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_log_file_ini0, inifile: tox.ini
collected 1 item

test_log_file_ini.py PASSED
.

=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
WARNING  catchlog:test_log_file_ini.py:7 This log message will be shown
___________________________ test_log_file_ini_level ____________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_log_file_ini_level0, inifile: tox.ini
collected 1 item

test_log_file_ini_level.py PASSED
.

=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
INFO     catchlog:test_log_file_ini_level.py:7 This log message will be shown
____________________________ test_log_file_unicode _____________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_log_file_unicode0, inifile: tox.ini
collected 1 item

test_log_file_unicode.py .                                               [100%]

=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
INFO     catchlog:test_log_file_unicode.py:6 Normal message
INFO     catchlog:test_log_file_unicode.py:7 ├
INFO     catchlog:test_log_file_unicode.py:8 Another normal message
_________________________ test_collection_live_logging _________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-8/test_collection_live_logging0
collecting ...
----------------------------- live log collection ------------------------------
INFO     root:test_collection_live_logging.py:3 Normal message
collected 0 items

========================= no tests ran in 0.01 seconds =========================
------------------------------ Captured log call -------------------------------
INFO     root:test_collection_live_logging.py:3 Normal message
_______________________ test_collection_logging_to_file ________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1
rootdir: /tmp/pytest-of-root/pytest-8/test_collection_logging_to_file0, inifile: tox.ini
collected 1 item

test_collection_logging_to_file.py .                                     [100%]

=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
INFO     root:test_collection_logging_to_file.py:3 Normal message
INFO     root:test_collection_logging_to_file.py:7 info message in test_simple
______________________________ test_log_in_hooks _______________________________
----------------------------- Captured stdout call -----------------------------

---------------------------- live log sessionstart -----------------------------
INFO     root:conftest.py:7 sessionstart
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-8/test_log_in_hooks0, inifile: tox.ini
collecting ... collected 0 items
----------------------------- live log collection ------------------------------
INFO     root:conftest.py:4 runtestloop
---------------------------- live log sessionfinish ----------------------------
INFO     root:conftest.py:10 sessionfinish

========================= no tests ran in 0.00 seconds =========================
------------------------------ Captured log call -------------------------------
INFO     root:conftest.py:7 sessionstart
INFO     root:conftest.py:4 runtestloop
INFO     root:conftest.py:10 sessionfinish
________________________ test_log_in_runtest_logreport _________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-8/test_log_in_runtest_logreport0, inifile: tox.ini
collecting ... collected 1 item

test_log_in_runtest_logreport.py::test_first
------------------------------ live log logreport ------------------------------
INFO     conftest:conftest.py:5 logreport
PASSED                                                                   [100%]------------------------------ live log logreport ------------------------------
INFO     conftest:conftest.py:5 logreport
------------------------------ live log logreport ------------------------------
INFO     conftest:conftest.py:5 logreport


=========================== 1 passed in 0.01 seconds ===========================
------------------------------ Captured log call -------------------------------
INFO     conftest:conftest.py:5 logreport
INFO     conftest:conftest.py:5 logreport
INFO     conftest:conftest.py:5 logreport
______________________________ test_log_set_path _______________________________
----------------------------- Captured stdout call -----------------------------
============================= test session starts ==============================
platform linux -- Python 3.9.19, pytest-4.4.2.dev176+g2051e30b9, py-1.11.0, pluggy-0.13.1 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .pytest_cache
rootdir: /tmp/pytest-of-root/pytest-8/test_log_set_path0, inifile: tox.ini
collecting ... collected 2 items

test_log_set_path.py::test_first
-------------------------------- live log call ---------------------------------
INFO     testcase-logger:test_log_set_path.py:4 message from test 1
PASSED                                                                   [ 50%]
test_log_set_path.py::test_second
-------------------------------- live log call ---------------------------------
DEBUG    testcase-logger:test_log_set_path.py:8 message from test 2
PASSED                                                                   [100%]

=========================== 2 passed in 0.01 seconds ===========================
----------------------------- Captured stderr call -----------------------------
Exception ignored in: <_io.FileIO name='/tmp/pytest-of-root/pytest-8/test_log_set_path0/test_first' mode='wb' closefd=True>
Traceback (most recent call last):
  File "/testbed/src/_pytest/logging.py", line 479, in set_log_path
    self.log_file_handler = logging.FileHandler(
ResourceWarning: unclosed file <_io.TextIOWrapper name='/tmp/pytest-of-root/pytest-8/test_log_set_path0/test_first' mode='w' encoding='UTF-8'>
------------------------------ Captured log call -------------------------------
INFO     testcase-logger:test_log_set_path.py:4 message from test 1
DEBUG    testcase-logger:test_log_set_path.py:8 message from test 2
=========================== short test summary info ============================
PASSED testing/logging/test_reporting.py::test_live_logging_suspends_capture[True]
PASSED testing/logging/test_reporting.py::test_live_logging_suspends_capture[False]
PASSED testing/logging/test_reporting.py::test_nothing_logged
PASSED testing/logging/test_reporting.py::test_messages_logged
PASSED testing/logging/test_reporting.py::test_root_logger_affected
PASSED testing/logging/test_reporting.py::test_log_cli_level_log_level_interaction
PASSED testing/logging/test_reporting.py::test_setup_logging
PASSED testing/logging/test_reporting.py::test_teardown_logging
PASSED testing/logging/test_reporting.py::test_disable_log_capturing
PASSED testing/logging/test_reporting.py::test_disable_log_capturing_ini
PASSED testing/logging/test_reporting.py::test_log_cli_enabled_disabled[True]
PASSED testing/logging/test_reporting.py::test_log_cli_enabled_disabled[False]
PASSED testing/logging/test_reporting.py::test_log_cli_default_level
PASSED testing/logging/test_reporting.py::test_log_cli_default_level_multiple_tests
PASSED testing/logging/test_reporting.py::test_log_cli_default_level_sections
PASSED testing/logging/test_reporting.py::test_live_logs_unknown_sections
PASSED testing/logging/test_reporting.py::test_sections_single_new_line_after_test_outcome
PASSED testing/logging/test_reporting.py::test_log_cli_level
PASSED testing/logging/test_reporting.py::test_log_cli_ini_level
PASSED testing/logging/test_reporting.py::test_log_cli_auto_enable[]
PASSED testing/logging/test_reporting.py::test_log_cli_auto_enable[--log-level=WARNING]
PASSED testing/logging/test_reporting.py::test_log_cli_auto_enable[--log-file-level=WARNING]
PASSED testing/logging/test_reporting.py::test_log_cli_auto_enable[--log-cli-level=WARNING]
PASSED testing/logging/test_reporting.py::test_log_file_cli
PASSED testing/logging/test_reporting.py::test_log_file_cli_level
PASSED testing/logging/test_reporting.py::test_log_level_not_changed_by_default
PASSED testing/logging/test_reporting.py::test_log_file_ini
PASSED testing/logging/test_reporting.py::test_log_file_ini_level
PASSED testing/logging/test_reporting.py::test_log_file_unicode
PASSED testing/logging/test_reporting.py::test_collection_live_logging
PASSED testing/logging/test_reporting.py::test_collection_logging_to_file
PASSED testing/logging/test_reporting.py::test_log_in_hooks
PASSED testing/logging/test_reporting.py::test_log_in_runtest_logreport
PASSED testing/logging/test_reporting.py::test_log_set_path
========================== 34 passed in 1.31 seconds ===========================
Updated 1 path from 0416a4fc1