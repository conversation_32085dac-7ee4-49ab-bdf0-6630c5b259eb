py39: commands[0]> python -X dev -m pytest --durations 25 tests/roots/test-ext-autodoc/target/annotations.py tests/test_ext_autodoc_configs.py
[1m============================= test session starts ==============================[0m
platform linux -- Python 3.9.19, pytest-8.2.2, pluggy-1.5.0
cachedir: .tox/py39/.pytest_cache
libraries: Sphinx-3.4.0+/5d8d6275a, docutils-0.21.2
base tempdir: /tmp/pytest-of-root/pytest-4
rootdir: /testbed
configfile: setup.cfg
plugins: cov-5.0.0
collected 0 items / 1 error

==================================== ERRORS ====================================
[31m[1m______________ ERROR collecting tests/test_ext_autodoc_configs.py ______________[0m
[31mImportError while importing test module '/testbed/tests/test_ext_autodoc_configs.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/opt/miniconda3/envs/testbed/lib/python3.9/importlib/__init__.py:127: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/test_ext_autodoc_configs.py:15: in <module>
    from test_ext_autodoc import do_autodoc
tests/test_ext_autodoc.py:20: in <module>
    from sphinx.ext.autodoc import ALL, ModuleLevelDocumenter, Options
sphinx/ext/autodoc/__init__.py:26: in <module>
    from sphinx.util.typing import stringify_annotation
E   ImportError: cannot import name 'stringify_annotation' from 'sphinx.util.typing' (/testbed/sphinx/util/typing.py)[0m
[33m=============================== warnings summary ===============================[0m
sphinx/util/docutils.py:45
  /testbed/sphinx/util/docutils.py:45: DeprecationWarning: distutils Version classes are deprecated. Use packaging.version instead.
    __version_info__ = tuple(LooseVersion(docutils.__version__).version)

sphinx/registry.py:22
  /testbed/sphinx/registry.py:22: DeprecationWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html
    from pkg_resources import iter_entry_points

../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:2832
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:2832
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:2832
../opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:2832
  /opt/miniconda3/envs/testbed/lib/python3.9/site-packages/pkg_resources/__init__.py:2832: DeprecationWarning: Deprecated call to `pkg_resources.declare_namespace('sphinxcontrib')`.
  Implementing implicit namespace packages (as specified in PEP 420) is preferred to `pkg_resources.declare_namespace`. See https://setuptools.pypa.io/en/latest/references/keywords.html#keyword-namespace-packages
    declare_namespace(pkg)

sphinx/directives/patches.py:14
  /testbed/sphinx/directives/patches.py:14: DeprecationWarning: The `docutils.parsers.rst.directive.html` module will be removed in Docutils 2.0. Since Docutils 0.18, the "Meta" node is defined in `docutils.parsers.rst.directives.misc`.
    from docutils.parsers.rst.directives import html, images, tables

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html
[36m[1m=========================== short test summary info ============================[0m
[31mERROR[0m tests/test_ext_autodoc_configs.py
!!!!!!!!!!!!!!!!!!!! Interrupted: 1 error during collection !!!!!!!!!!!!!!!!!!!!
[31m========================= [33m7 warnings[0m, [31m[1m1 error[0m[31m in 0.41s[0m[31m =========================[0m
py39: exit 2 (0.98 seconds) /testbed> python -X dev -m pytest --durations 25 tests/roots/test-ext-autodoc/target/annotations.py tests/test_ext_autodoc_configs.py pid=1360
  py39: FAIL code 2 (0.99=setup[0.01]+cmd[0.98] seconds)
  evaluation failed :( (1.12 seconds)
Updated 2 paths from 9e3d5e25e
