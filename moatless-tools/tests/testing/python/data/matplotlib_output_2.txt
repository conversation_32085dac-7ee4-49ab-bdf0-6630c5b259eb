============================= test session starts ==============================
platform linux -- Python 3.11.9, pytest-8.2.2, pluggy-1.5.0
rootdir: /testbed
configfile: pytest.ini
plugins: rerunfailures-14.0, timeout-2.3.1, cov-5.0.0, xdist-3.6.1
collected 253 items

lib/matplotlib/tests/test_colors.py .......F............................ [ 14%]
........................................................................ [ 42%]
........................................................................ [ 71%]
.....................................................................s.. [ 99%]
.                                                                        [100%]

=================================== FAILURES ===================================
______________________ test_double_register_builtin_cmap _______________________

    def test_double_register_builtin_cmap():
        name = "viridis"
        match = f"Re-registering the builtin cmap {name!r}."
        with pytest.raises(ValueError, match=match):
            matplotlib.colormaps.register(
                mpl.colormaps[name], name=name, force=True
            )
        with pytest.raises(ValueError, match='A colormap named "viridis"'):
            with pytest.warns(mpl.MatplotlibDeprecationWarning):
                cm.register_cmap(name, mpl.colormaps[name])
>       with pytest.warns(UserWarning):
E       matplotlib._api.deprecation.MatplotlibDeprecationWarning: The register_cmap function was deprecated in Matplotlib 3.7 and will be removed two minor releases later. Use ``matplotlib.colormaps.register(name)`` instead.

lib/matplotlib/tests/test_colors.py:150: MatplotlibDeprecationWarning
==================================== PASSES ====================================
=========================== short test summary info ============================
PASSED lib/matplotlib/tests/test_colors.py::test_create_lookup_table[5-result0]
PASSED lib/matplotlib/tests/test_colors.py::test_create_lookup_table[2-result1]
PASSED lib/matplotlib/tests/test_colors.py::test_create_lookup_table[1-result2]
PASSED lib/matplotlib/tests/test_colors.py::test_resampled
PASSED lib/matplotlib/tests/test_colors.py::test_numpy_1_24_deprecation_warnings
PASSED lib/matplotlib/tests/test_colors.py::test_register_cmap
PASSED lib/matplotlib/tests/test_colors.py::test_colormaps_get_cmap
PASSED lib/matplotlib/tests/test_colors.py::test_unregister_builtin_cmap
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_copy
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_equals
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_endian
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_invalid
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_return_types
PASSED lib/matplotlib/tests/test_colors.py::test_BoundaryNorm
PASSED lib/matplotlib/tests/test_colors.py::test_CenteredNorm
PASSED lib/matplotlib/tests/test_colors.py::test_lognorm_invalid[-1-2]
PASSED lib/matplotlib/tests/test_colors.py::test_lognorm_invalid[3-1]
PASSED lib/matplotlib/tests/test_colors.py::test_LogNorm
PASSED lib/matplotlib/tests/test_colors.py::test_LogNorm_inverse
PASSED lib/matplotlib/tests/test_colors.py::test_PowerNorm
PASSED lib/matplotlib/tests/test_colors.py::test_PowerNorm_translation_invariance
PASSED lib/matplotlib/tests/test_colors.py::test_Normalize
PASSED lib/matplotlib/tests/test_colors.py::test_FuncNorm
PASSED lib/matplotlib/tests/test_colors.py::test_TwoSlopeNorm_autoscale
PASSED lib/matplotlib/tests/test_colors.py::test_TwoSlopeNorm_autoscale_None_vmin
PASSED lib/matplotlib/tests/test_colors.py::test_TwoSlopeNorm_autoscale_None_vmax
PASSED lib/matplotlib/tests/test_colors.py::test_TwoSlopeNorm_scale
PASSED lib/matplotlib/tests/test_colors.py::test_TwoSlopeNorm_scaleout_center
PASSED lib/matplotlib/tests/test_colors.py::test_TwoSlopeNorm_scaleout_center_max
PASSED lib/matplotlib/tests/test_colors.py::test_TwoSlopeNorm_Even
PASSED lib/matplotlib/tests/test_colors.py::test_TwoSlopeNorm_Odd
PASSED lib/matplotlib/tests/test_colors.py::test_TwoSlopeNorm_VminEqualsVcenter
PASSED lib/matplotlib/tests/test_colors.py::test_TwoSlopeNorm_VmaxEqualsVcenter
PASSED lib/matplotlib/tests/test_colors.py::test_TwoSlopeNorm_VminGTVcenter
PASSED lib/matplotlib/tests/test_colors.py::test_TwoSlopeNorm_TwoSlopeNorm_VminGTVmax
PASSED lib/matplotlib/tests/test_colors.py::test_TwoSlopeNorm_VcenterGTVmax
PASSED lib/matplotlib/tests/test_colors.py::test_TwoSlopeNorm_premature_scaling
PASSED lib/matplotlib/tests/test_colors.py::test_SymLogNorm
PASSED lib/matplotlib/tests/test_colors.py::test_SymLogNorm_colorbar
PASSED lib/matplotlib/tests/test_colors.py::test_SymLogNorm_single_zero
PASSED lib/matplotlib/tests/test_colors.py::TestAsinhNorm::test_init
PASSED lib/matplotlib/tests/test_colors.py::TestAsinhNorm::test_norm
PASSED lib/matplotlib/tests/test_colors.py::test_cmap_and_norm_from_levels_and_colors[png]
PASSED lib/matplotlib/tests/test_colors.py::test_boundarynorm_and_colorbarbase[png]
PASSED lib/matplotlib/tests/test_colors.py::test_cmap_and_norm_from_levels_and_colors2
PASSED lib/matplotlib/tests/test_colors.py::test_rgb_hsv_round_trip
PASSED lib/matplotlib/tests/test_colors.py::test_autoscale_masked
PASSED lib/matplotlib/tests/test_colors.py::test_light_source_topo_surface[png]
PASSED lib/matplotlib/tests/test_colors.py::test_light_source_shading_default
PASSED lib/matplotlib/tests/test_colors.py::test_light_source_shading_empty_mask
PASSED lib/matplotlib/tests/test_colors.py::test_light_source_masked_shading
PASSED lib/matplotlib/tests/test_colors.py::test_light_source_hillshading
PASSED lib/matplotlib/tests/test_colors.py::test_light_source_planar_hillshading
PASSED lib/matplotlib/tests/test_colors.py::test_color_names
PASSED lib/matplotlib/tests/test_colors.py::test_pandas_iterable
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Accent]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Accent_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Blues]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Blues_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[BrBG]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[BrBG_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[BuGn]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[BuGn_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[BuPu]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[BuPu_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[CMRmap]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[CMRmap_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Dark2]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Dark2_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[GnBu]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[GnBu_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Greens]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Greens_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Greys]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Greys_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[OrRd]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[OrRd_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Oranges]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Oranges_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[PRGn]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[PRGn_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Paired]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Paired_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Pastel1]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Pastel1_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Pastel2]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Pastel2_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[PiYG]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[PiYG_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[PuBu]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[PuBuGn]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[PuBuGn_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[PuBu_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[PuOr]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[PuOr_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[PuRd]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[PuRd_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Purples]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Purples_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[RdBu]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[RdBu_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[RdGy]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[RdGy_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[RdPu]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[RdPu_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[RdYlBu]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[RdYlBu_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[RdYlGn]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[RdYlGn_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Reds]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Reds_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Set1]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Set1_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Set2]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Set2_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Set3]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Set3_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Spectral]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Spectral_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Wistia]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[Wistia_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[YlGn]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[YlGnBu]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[YlGnBu_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[YlGn_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[YlOrBr]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[YlOrBr_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[YlOrRd]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[YlOrRd_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[afmhot]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[afmhot_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[autumn]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[autumn_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[binary]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[binary_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[bone]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[bone_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[brg]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[brg_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[bwr]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[bwr_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[cividis]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[cividis_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[cool]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[cool_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[coolwarm]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[coolwarm_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[copper]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[copper_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[cubehelix]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[cubehelix_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[flag]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[flag_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gist_earth]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gist_earth_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gist_gray]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gist_gray_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gist_heat]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gist_heat_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gist_ncar]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gist_ncar_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gist_rainbow]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gist_rainbow_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gist_stern]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gist_stern_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gist_yarg]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gist_yarg_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gnuplot]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gnuplot2]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gnuplot2_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gnuplot_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gray]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[gray_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[hot]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[hot_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[hsv]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[hsv_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[inferno]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[inferno_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[jet]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[jet_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[magma]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[magma_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[nipy_spectral]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[nipy_spectral_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[ocean]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[ocean_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[pink]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[pink_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[plasma]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[plasma_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[prism]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[prism_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[rainbow]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[rainbow_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[seismic]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[seismic_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[spring]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[spring_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[summer]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[summer_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[tab10]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[tab10_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[tab20]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[tab20_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[tab20b]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[tab20b_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[tab20c]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[tab20c_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[terrain]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[terrain_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[turbo]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[turbo_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[twilight]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[twilight_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[twilight_shifted]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[twilight_shifted_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[viridis]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[viridis_r]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[winter]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_reversing[winter_r]
PASSED lib/matplotlib/tests/test_colors.py::test_has_alpha_channel
PASSED lib/matplotlib/tests/test_colors.py::test_cn
PASSED lib/matplotlib/tests/test_colors.py::test_conversions
PASSED lib/matplotlib/tests/test_colors.py::test_conversions_masked
PASSED lib/matplotlib/tests/test_colors.py::test_to_rgba_array_single_str
PASSED lib/matplotlib/tests/test_colors.py::test_to_rgba_array_alpha_array
PASSED lib/matplotlib/tests/test_colors.py::test_failed_conversions
PASSED lib/matplotlib/tests/test_colors.py::test_grey_gray
PASSED lib/matplotlib/tests/test_colors.py::test_tableau_order
PASSED lib/matplotlib/tests/test_colors.py::test_ndarray_subclass_norm
PASSED lib/matplotlib/tests/test_colors.py::test_same_color
PASSED lib/matplotlib/tests/test_colors.py::test_hex_shorthand_notation
PASSED lib/matplotlib/tests/test_colors.py::test_repr_png
PASSED lib/matplotlib/tests/test_colors.py::test_repr_html
PASSED lib/matplotlib/tests/test_colors.py::test_get_under_over_bad
PASSED lib/matplotlib/tests/test_colors.py::test_non_mutable_get_values[over]
PASSED lib/matplotlib/tests/test_colors.py::test_non_mutable_get_values[under]
PASSED lib/matplotlib/tests/test_colors.py::test_non_mutable_get_values[bad]
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_alpha_array
PASSED lib/matplotlib/tests/test_colors.py::test_colormap_bad_data_with_alpha
PASSED lib/matplotlib/tests/test_colors.py::test_2d_to_rgba
PASSED lib/matplotlib/tests/test_colors.py::test_set_dict_to_rgba
PASSED lib/matplotlib/tests/test_colors.py::test_norm_deepcopy
PASSED lib/matplotlib/tests/test_colors.py::test_norm_callback
PASSED lib/matplotlib/tests/test_colors.py::test_scalarmappable_norm_update
PASSED lib/matplotlib/tests/test_colors.py::test_norm_update_figs[png]
PASSED lib/matplotlib/tests/test_colors.py::test_norm_update_figs[pdf]
PASSED lib/matplotlib/tests/test_colors.py::test_make_norm_from_scale_name
PASSED lib/matplotlib/tests/test_colors.py::test_color_sequences
PASSED lib/matplotlib/tests/test_colors.py::test_cm_set_cmap_error
SKIPPED [1] lib/matplotlib/testing/compare.py:285: Don't know how to convert .svg files to png
FAILED lib/matplotlib/tests/test_colors.py::test_double_register_builtin_cmap
=================== 1 failed, 251 passed, 1 skipped in 6.70s ===================

