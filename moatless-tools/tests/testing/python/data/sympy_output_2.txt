
============================= test process starts ==============================
executable:         /opt/miniconda3/envs/testbed/bin/python  (3.9.19-final-0) [CPython]
architecture:       64-bit
cache:              no
ground types:       python
numpy:              None
random seed:        75402163
hash randomization: on (PYTHONHASHSEED=630012298)

sympy/core/tests/test_args.py[862]
test_all_classes_are_tested ok
test_sympy__assumptions__assume__AppliedPredicate ok
test_sympy__assumptions__assume__Predicate ok
test_sympy__assumptions__sathandlers__UnevaluatedOnFree ok
test_sympy__assumptions__sathandlers__AllArgs ok
test_sympy__assumptions__sathandlers__AnyArgs ok
test_sympy__assumptions__sathandlers__ExactlyOneArg ok
test_sympy__assumptions__sathandlers__CheckOldAssump ok
test_sympy__assumptions__sathandlers__CheckIsPrime ok
test_sympy__codegen__ast__AssignmentBase abstract Class s
test_sympy__codegen__ast__AugmentedAssignment abstract Class s
test_sympy__codegen__ast__AddAugmentedAssignment ok
test_sympy__codegen__ast__SubAugmentedAssignment ok
test_sympy__codegen__ast__MulAugmentedAssignment ok
test_sympy__codegen__ast__DivAugmentedAssignment ok
test_sympy__codegen__ast__ModAugmentedAssignment ok
test_sympy__codegen__ast__CodeBlock ok
test_sympy__codegen__ast__For ok
test_sympy__codegen__ast__Token ok
test_sympy__codegen__ast__ContinueToken ok
test_sympy__codegen__ast__BreakToken ok
test_sympy__codegen__ast__NoneToken ok
test_sympy__codegen__ast__String ok
test_sympy__codegen__ast__QuotedString ok
test_sympy__codegen__ast__Comment ok
test_sympy__codegen__ast__Node ok
test_sympy__codegen__ast__Type ok
test_sympy__codegen__ast__IntBaseType ok
test_sympy__codegen__ast___SizedIntType ok
test_sympy__codegen__ast__SignedIntType ok
test_sympy__codegen__ast__UnsignedIntType ok
test_sympy__codegen__ast__FloatBaseType ok
test_sympy__codegen__ast__FloatType ok
test_sympy__codegen__ast__ComplexBaseType ok
test_sympy__codegen__ast__ComplexType ok
test_sympy__codegen__ast__Attribute ok
test_sympy__codegen__ast__Variable ok
test_sympy__codegen__ast__Pointer ok
test_sympy__codegen__ast__Declaration ok
test_sympy__codegen__ast__While ok
test_sympy__codegen__ast__Scope ok
test_sympy__codegen__ast__Stream ok
test_sympy__codegen__ast__Print ok
test_sympy__codegen__ast__FunctionPrototype ok
test_sympy__codegen__ast__FunctionDefinition ok
test_sympy__codegen__ast__Return ok
test_sympy__codegen__ast__FunctionCall ok
test_sympy__codegen__ast__Element ok
test_sympy__codegen__cnodes__CommaOperator ok
test_sympy__codegen__cnodes__goto ok
test_sympy__codegen__cnodes__Label ok
test_sympy__codegen__cnodes__PreDecrement ok
test_sympy__codegen__cnodes__PostDecrement ok
test_sympy__codegen__cnodes__PreIncrement ok
test_sympy__codegen__cnodes__PostIncrement ok
test_sympy__codegen__cnodes__struct ok
test_sympy__codegen__cnodes__union ok
test_sympy__codegen__cxxnodes__using ok
test_sympy__codegen__fnodes__Program ok
test_sympy__codegen__fnodes__Module ok
test_sympy__codegen__fnodes__Subroutine ok
test_sympy__codegen__fnodes__GoTo ok
test_sympy__codegen__fnodes__FortranReturn ok
test_sympy__codegen__fnodes__Extent ok
test_sympy__codegen__fnodes__use_rename ok
test_sympy__codegen__fnodes__use ok
test_sympy__codegen__fnodes__SubroutineCall ok
test_sympy__codegen__fnodes__Do ok
test_sympy__codegen__fnodes__ImpliedDoLoop ok
test_sympy__codegen__fnodes__ArrayConstructor ok
test_sympy__codegen__fnodes__sum_ ok
test_sympy__codegen__fnodes__product_ ok
test_sympy__combinatorics__graycode__GrayCode f
test_sympy__combinatorics__subsets__Subset ok
test_sympy__combinatorics__permutations__Permutation f
test_sympy__combinatorics__perm_groups__PermutationGroup ok
test_sympy__combinatorics__polyhedron__Polyhedron ok
test_sympy__combinatorics__prufer__Prufer f
test_sympy__combinatorics__partitions__Partition ok
test_sympy__combinatorics__partitions__IntegerPartition f
test_sympy__concrete__products__Product ok
test_sympy__concrete__expr_with_limits__ExprWithLimits abstract Class s
test_sympy__concrete__expr_with_limits__AddWithLimits abstract Class s
test_sympy__concrete__expr_with_intlimits__ExprWithIntLimits abstract Class s
test_sympy__concrete__summations__Sum ok
test_sympy__core__add__Add ok
test_sympy__core__basic__Atom ok
test_sympy__core__basic__Basic ok
test_sympy__core__containers__Dict ok
test_sympy__core__containers__Tuple ok
test_sympy__core__expr__AtomicExpr ok
test_sympy__core__expr__Expr ok
test_sympy__core__expr__UnevaluatedExpr ok
test_sympy__core__function__Application ok
test_sympy__core__function__AppliedUndef ok
test_sympy__core__function__Derivative ok
test_sympy__core__function__Function abstract class s
test_sympy__core__function__Lambda ok
test_sympy__core__function__Subs ok
test_sympy__core__function__WildFunction ok
test_sympy__core__mod__Mod ok
test_sympy__core__mul__Mul ok
test_sympy__core__numbers__Catalan ok
test_sympy__core__numbers__ComplexInfinity ok
test_sympy__core__numbers__EulerGamma ok
test_sympy__core__numbers__Exp1 ok
test_sympy__core__numbers__Float ok
test_sympy__core__numbers__GoldenRatio ok
test_sympy__core__numbers__TribonacciConstant ok
test_sympy__core__numbers__Half ok
test_sympy__core__numbers__ImaginaryUnit ok
test_sympy__core__numbers__Infinity ok
test_sympy__core__numbers__Integer ok
test_sympy__core__numbers__IntegerConstant abstract class s
test_sympy__core__numbers__NaN ok
test_sympy__core__numbers__NegativeInfinity ok
test_sympy__core__numbers__NegativeOne ok
test_sympy__core__numbers__Number ok
test_sympy__core__numbers__NumberSymbol ok
test_sympy__core__numbers__One ok
test_sympy__core__numbers__Pi ok
test_sympy__core__numbers__Rational ok
test_sympy__core__numbers__RationalConstant abstract class s
test_sympy__core__numbers__Zero ok
test_sympy__core__operations__AssocOp abstract class s
test_sympy__core__operations__LatticeOp abstract class s
test_sympy__core__power__Pow ok
test_sympy__algebras__quaternion__Quaternion ok
test_sympy__core__relational__Equality ok
test_sympy__core__relational__GreaterThan ok
test_sympy__core__relational__LessThan ok
test_sympy__core__relational__Relational abstract class s
test_sympy__core__relational__StrictGreaterThan ok
test_sympy__core__relational__StrictLessThan ok
test_sympy__core__relational__Unequality ok
test_sympy__sandbox__indexed_integrals__IndexedIntegral ok
test_sympy__calculus__util__AccumulationBounds ok
test_sympy__sets__ordinals__OmegaPower ok
test_sympy__sets__ordinals__Ordinal ok
test_sympy__sets__ordinals__OrdinalOmega ok
test_sympy__sets__ordinals__OrdinalZero ok
test_sympy__sets__sets__EmptySet ok
test_sympy__sets__sets__UniversalSet ok
test_sympy__sets__sets__FiniteSet ok
test_sympy__sets__sets__Interval ok
test_sympy__sets__sets__ProductSet ok
test_sympy__sets__sets__Set does it make sense to test this? s
test_sympy__sets__sets__Intersection ok
test_sympy__sets__sets__Union ok
test_sympy__sets__sets__Complement ok
test_sympy__sets__sets__SymmetricDifference ok
test_sympy__core__trace__Tr ok
test_sympy__sets__setexpr__SetExpr ok
test_sympy__sets__fancysets__Rationals ok
test_sympy__sets__fancysets__Naturals ok
test_sympy__sets__fancysets__Naturals0 ok
test_sympy__sets__fancysets__Integers ok
test_sympy__sets__fancysets__Reals ok
test_sympy__sets__fancysets__Complexes ok
test_sympy__sets__fancysets__ComplexRegion ok
test_sympy__sets__fancysets__ImageSet ok
test_sympy__sets__fancysets__Range ok
test_sympy__sets__conditionset__ConditionSet ok
test_sympy__sets__contains__Contains ok
test_sympy__stats__crv__ContinuousDomain ok
test_sympy__stats__crv__SingleContinuousDomain ok
test_sympy__stats__crv__ProductContinuousDomain ok
test_sympy__stats__crv__ConditionalContinuousDomain ok
test_sympy__stats__crv__ContinuousPSpace ok
test_sympy__stats__crv__SingleContinuousPSpace ok
test_sympy__stats__crv__SingleContinuousDistribution abstract class s
test_sympy__stats__drv__SingleDiscreteDomain ok
test_sympy__stats__drv__ProductDiscreteDomain ok
test_sympy__stats__drv__SingleDiscretePSpace ok
test_sympy__stats__drv__DiscretePSpace ok
test_sympy__stats__drv__ConditionalDiscreteDomain ok
test_sympy__stats__joint_rv__JointPSpace ok
test_sympy__stats__joint_rv__JointRandomSymbol ok
test_sympy__stats__joint_rv__JointDistributionHandmade ok
test_sympy__stats__joint_rv__MarginalDistribution ok
test_sympy__stats__joint_rv__CompoundDistribution ok
test_sympy__stats__drv__SingleDiscreteDistribution abstract class s
test_sympy__stats__drv__DiscreteDistribution abstract class s
test_sympy__stats__drv__DiscreteDomain abstract class s
test_sympy__stats__rv__RandomDomain ok
test_sympy__stats__rv__SingleDomain ok
test_sympy__stats__rv__ConditionalDomain ok
test_sympy__stats__rv__PSpace ok
test_sympy__stats__rv__SinglePSpace abstract Class s
test_sympy__stats__rv__RandomSymbol ok
test_sympy__stats__rv__ProductPSpace abstract Class s
test_sympy__stats__rv__IndependentProductPSpace ok
test_sympy__stats__rv__ProductDomain ok
test_sympy__stats__symbolic_probability__Probability ok
test_sympy__stats__symbolic_probability__Expectation ok
test_sympy__stats__symbolic_probability__Covariance ok
test_sympy__stats__symbolic_probability__Variance ok
test_sympy__stats__frv_types__DiscreteUniformDistribution ok
test_sympy__stats__frv_types__DieDistribution ok
test_sympy__stats__frv_types__BernoulliDistribution ok
test_sympy__stats__frv_types__BinomialDistribution ok
test_sympy__stats__frv_types__BetaBinomialDistribution ok
test_sympy__stats__frv_types__HypergeometricDistribution ok
test_sympy__stats__frv_types__RademacherDistribution ok
test_sympy__stats__frv__FiniteDomain ok
test_sympy__stats__frv__SingleFiniteDomain ok
test_sympy__stats__frv__ProductFiniteDomain ok
test_sympy__stats__frv__ConditionalFiniteDomain ok
test_sympy__stats__frv__FinitePSpace ok
test_sympy__stats__frv__SingleFinitePSpace ok
test_sympy__stats__frv__ProductFinitePSpace ok
test_sympy__stats__frv__SingleFiniteDistribution abstract class s
test_sympy__stats__crv__ContinuousDistribution abstract class s
test_sympy__stats__frv_types__FiniteDistributionHandmade ok
test_sympy__stats__crv__ContinuousDistributionHandmade ok
test_sympy__stats__drv__DiscreteDistributionHandmade ok
test_sympy__stats__rv__Density ok
test_sympy__stats__crv_types__ArcsinDistribution ok
test_sympy__stats__crv_types__BeniniDistribution ok
test_sympy__stats__crv_types__BetaDistribution ok
test_sympy__stats__crv_types__BetaNoncentralDistribution ok
test_sympy__stats__crv_types__BetaPrimeDistribution ok
test_sympy__stats__crv_types__CauchyDistribution ok
test_sympy__stats__crv_types__ChiDistribution ok
test_sympy__stats__crv_types__ChiNoncentralDistribution ok
test_sympy__stats__crv_types__ChiSquaredDistribution ok
test_sympy__stats__crv_types__DagumDistribution ok
test_sympy__stats__crv_types__ExponentialDistribution ok
test_sympy__stats__crv_types__FDistributionDistribution ok
test_sympy__stats__crv_types__FisherZDistribution ok
test_sympy__stats__crv_types__FrechetDistribution ok
test_sympy__stats__crv_types__GammaInverseDistribution ok
test_sympy__stats__crv_types__GammaDistribution ok
test_sympy__stats__crv_types__GumbelDistribution ok
test_sympy__stats__crv_types__GompertzDistribution ok
test_sympy__stats__crv_types__KumaraswamyDistribution ok
test_sympy__stats__crv_types__LaplaceDistribution ok
test_sympy__stats__crv_types__LogisticDistribution ok
test_sympy__stats__crv_types__LogLogisticDistribution ok
test_sympy__stats__crv_types__LogNormalDistribution ok
test_sympy__stats__crv_types__MaxwellDistribution ok
test_sympy__stats__crv_types__NakagamiDistribution ok
test_sympy__stats__crv_types__NormalDistribution ok
test_sympy__stats__crv_types__GaussianInverseDistribution ok
test_sympy__stats__crv_types__ParetoDistribution ok
test_sympy__stats__crv_types__QuadraticUDistribution ok
test_sympy__stats__crv_types__RaisedCosineDistribution ok
test_sympy__stats__crv_types__RayleighDistribution ok
test_sympy__stats__crv_types__ShiftedGompertzDistribution ok
test_sympy__stats__crv_types__StudentTDistribution ok
test_sympy__stats__crv_types__TrapezoidalDistribution ok
test_sympy__stats__crv_types__TriangularDistribution ok
test_sympy__stats__crv_types__UniformDistribution ok
test_sympy__stats__crv_types__UniformSumDistribution ok
test_sympy__stats__crv_types__VonMisesDistribution ok
test_sympy__stats__crv_types__WeibullDistribution ok
test_sympy__stats__crv_types__WignerSemicircleDistribution ok
test_sympy__stats__drv_types__GeometricDistribution ok
test_sympy__stats__drv_types__LogarithmicDistribution ok
test_sympy__stats__drv_types__NegativeBinomialDistribution ok
test_sympy__stats__drv_types__PoissonDistribution ok
test_sympy__stats__drv_types__YuleSimonDistribution ok
test_sympy__stats__drv_types__ZetaDistribution ok
test_sympy__stats__joint_rv__JointDistribution ok
test_sympy__stats__joint_rv_types__MultivariateNormalDistribution ok
test_sympy__stats__joint_rv_types__MultivariateLaplaceDistribution ok
test_sympy__stats__joint_rv_types__MultivariateTDistribution ok
test_sympy__stats__joint_rv_types__NormalGammaDistribution ok
test_sympy__stats__joint_rv_types__GeneralizedMultivariateLogGammaDistribution ok
test_sympy__stats__joint_rv_types__MultivariateBetaDistribution ok
test_sympy__stats__joint_rv_types__MultivariateEwensDistribution ok
test_sympy__stats__joint_rv_types__MultinomialDistribution ok
test_sympy__stats__joint_rv_types__NegativeMultinomialDistribution ok
test_sympy__core__symbol__Dummy ok
test_sympy__core__symbol__Symbol ok
test_sympy__core__symbol__Wild ok
test_sympy__functions__combinatorial__factorials__CombinatorialFunction abstract class
s
test_sympy__functions__combinatorial__factorials__FallingFactorial ok
test_sympy__functions__combinatorial__factorials__MultiFactorial ok
test_sympy__functions__combinatorial__factorials__RisingFactorial ok
test_sympy__functions__combinatorial__factorials__binomial ok
test_sympy__functions__combinatorial__factorials__subfactorial ok
test_sympy__functions__combinatorial__factorials__factorial ok
test_sympy__functions__combinatorial__factorials__factorial2 ok
test_sympy__functions__combinatorial__numbers__bell ok
test_sympy__functions__combinatorial__numbers__bernoulli ok
test_sympy__functions__combinatorial__numbers__catalan ok
test_sympy__functions__combinatorial__numbers__genocchi ok
test_sympy__functions__combinatorial__numbers__euler ok
test_sympy__functions__combinatorial__numbers__carmichael ok
test_sympy__functions__combinatorial__numbers__fibonacci ok
test_sympy__functions__combinatorial__numbers__tribonacci ok
test_sympy__functions__combinatorial__numbers__harmonic ok
test_sympy__functions__combinatorial__numbers__lucas ok
test_sympy__functions__combinatorial__numbers__partition ok
test_sympy__functions__elementary__complexes__Abs ok
test_sympy__functions__elementary__complexes__adjoint ok
test_sympy__functions__elementary__complexes__arg ok
test_sympy__functions__elementary__complexes__conjugate ok
test_sympy__functions__elementary__complexes__im ok
test_sympy__functions__elementary__complexes__re ok
test_sympy__functions__elementary__complexes__sign ok
test_sympy__functions__elementary__complexes__polar_lift ok
test_sympy__functions__elementary__complexes__periodic_argument ok
test_sympy__functions__elementary__complexes__principal_branch ok
test_sympy__functions__elementary__complexes__transpose ok
test_sympy__functions__elementary__exponential__LambertW ok
test_sympy__functions__elementary__exponential__ExpBase abstract class s
test_sympy__functions__elementary__exponential__exp ok
test_sympy__functions__elementary__exponential__exp_polar ok
test_sympy__functions__elementary__exponential__log ok
test_sympy__functions__elementary__hyperbolic__HyperbolicFunction abstract class
s
test_sympy__functions__elementary__hyperbolic__ReciprocalHyperbolicFunction abstract class
s
test_sympy__functions__elementary__hyperbolic__InverseHyperbolicFunction abstract class
s
test_sympy__functions__elementary__hyperbolic__acosh ok
test_sympy__functions__elementary__hyperbolic__acoth ok
test_sympy__functions__elementary__hyperbolic__asinh ok
test_sympy__functions__elementary__hyperbolic__atanh ok
test_sympy__functions__elementary__hyperbolic__asech ok
test_sympy__functions__elementary__hyperbolic__acsch ok
test_sympy__functions__elementary__hyperbolic__cosh ok
test_sympy__functions__elementary__hyperbolic__coth ok
test_sympy__functions__elementary__hyperbolic__csch ok
test_sympy__functions__elementary__hyperbolic__sech ok
test_sympy__functions__elementary__hyperbolic__sinh ok
test_sympy__functions__elementary__hyperbolic__tanh ok
test_sympy__functions__elementary__integers__RoundFunction does this work at all?
s
test_sympy__functions__elementary__integers__ceiling ok
test_sympy__functions__elementary__integers__floor ok
test_sympy__functions__elementary__integers__frac ok
test_sympy__functions__elementary__miscellaneous__IdentityFunction ok
test_sympy__functions__elementary__miscellaneous__Max ok
test_sympy__functions__elementary__miscellaneous__Min ok
test_sympy__functions__elementary__miscellaneous__MinMaxBase abstract class s
test_sympy__functions__elementary__piecewise__ExprCondPair ok
test_sympy__functions__elementary__piecewise__Piecewise ok
test_sympy__functions__elementary__trigonometric__TrigonometricFunction abstract class
s
test_sympy__functions__elementary__trigonometric__ReciprocalTrigonometricFunction
abstract class s
test_sympy__functions__elementary__trigonometric__InverseTrigonometricFunction abstract class
s
test_sympy__functions__elementary__trigonometric__acos ok
test_sympy__functions__elementary__trigonometric__acot ok
test_sympy__functions__elementary__trigonometric__asin ok
test_sympy__functions__elementary__trigonometric__asec ok
test_sympy__functions__elementary__trigonometric__acsc ok
test_sympy__functions__elementary__trigonometric__atan ok
test_sympy__functions__elementary__trigonometric__atan2 ok
test_sympy__functions__elementary__trigonometric__cos ok
test_sympy__functions__elementary__trigonometric__csc ok
test_sympy__functions__elementary__trigonometric__cot ok
test_sympy__functions__elementary__trigonometric__sin ok
test_sympy__functions__elementary__trigonometric__sinc ok
test_sympy__functions__elementary__trigonometric__sec ok
test_sympy__functions__elementary__trigonometric__tan ok
test_sympy__functions__special__bessel__BesselBase abstract class s
test_sympy__functions__special__bessel__SphericalBesselBase abstract class s
test_sympy__functions__special__bessel__SphericalHankelBase abstract class s
test_sympy__functions__special__bessel__besseli ok
test_sympy__functions__special__bessel__besselj ok
test_sympy__functions__special__bessel__besselk ok
test_sympy__functions__special__bessel__bessely ok
test_sympy__functions__special__bessel__hankel1 ok
test_sympy__functions__special__bessel__hankel2 ok
test_sympy__functions__special__bessel__jn ok
test_sympy__functions__special__bessel__yn ok
test_sympy__functions__special__bessel__hn1 ok
test_sympy__functions__special__bessel__hn2 ok
test_sympy__functions__special__bessel__AiryBase ok
test_sympy__functions__special__bessel__airyai ok
test_sympy__functions__special__bessel__airybi ok
test_sympy__functions__special__bessel__airyaiprime ok
test_sympy__functions__special__bessel__airybiprime ok
test_sympy__functions__special__elliptic_integrals__elliptic_k ok
test_sympy__functions__special__elliptic_integrals__elliptic_f ok
test_sympy__functions__special__elliptic_integrals__elliptic_e ok
test_sympy__functions__special__elliptic_integrals__elliptic_pi ok
test_sympy__functions__special__delta_functions__DiracDelta ok
test_sympy__functions__special__singularity_functions__SingularityFunction ok
test_sympy__functions__special__delta_functions__Heaviside ok
test_sympy__functions__special__error_functions__erf ok
test_sympy__functions__special__error_functions__erfc ok
test_sympy__functions__special__error_functions__erfi ok
test_sympy__functions__special__error_functions__erf2 ok
test_sympy__functions__special__error_functions__erfinv ok
test_sympy__functions__special__error_functions__erfcinv ok
test_sympy__functions__special__error_functions__erf2inv ok
test_sympy__functions__special__error_functions__FresnelIntegral abstract class
s
test_sympy__functions__special__error_functions__fresnels ok
test_sympy__functions__special__error_functions__fresnelc ok
test_sympy__functions__special__error_functions__erfs ok
test_sympy__functions__special__error_functions__Ei ok
test_sympy__functions__special__error_functions__li ok
test_sympy__functions__special__error_functions__Li ok
test_sympy__functions__special__error_functions__TrigonometricIntegral abstract class
s
test_sympy__functions__special__error_functions__Si ok
test_sympy__functions__special__error_functions__Ci ok
test_sympy__functions__special__error_functions__Shi ok
test_sympy__functions__special__error_functions__Chi ok
test_sympy__functions__special__error_functions__expint ok
test_sympy__functions__special__gamma_functions__gamma ok
test_sympy__functions__special__gamma_functions__loggamma ok
test_sympy__functions__special__gamma_functions__lowergamma ok
test_sympy__functions__special__gamma_functions__polygamma ok
test_sympy__functions__special__gamma_functions__uppergamma ok
test_sympy__functions__special__beta_functions__beta ok
test_sympy__functions__special__mathieu_functions__MathieuBase ok
test_sympy__functions__special__mathieu_functions__mathieus ok
test_sympy__functions__special__mathieu_functions__mathieuc ok
test_sympy__functions__special__mathieu_functions__mathieusprime ok
test_sympy__functions__special__mathieu_functions__mathieucprime ok
test_sympy__functions__special__hyper__TupleParametersBase abstract class s
test_sympy__functions__special__hyper__TupleArg abstract class s
test_sympy__functions__special__hyper__hyper ok
test_sympy__functions__special__hyper__meijerg ok
test_sympy__functions__special__hyper__HyperRep abstract class s
test_sympy__functions__special__hyper__HyperRep_power1 ok
test_sympy__functions__special__hyper__HyperRep_power2 ok
test_sympy__functions__special__hyper__HyperRep_log1 ok
test_sympy__functions__special__hyper__HyperRep_atanh ok
test_sympy__functions__special__hyper__HyperRep_asin1 ok
test_sympy__functions__special__hyper__HyperRep_asin2 ok
test_sympy__functions__special__hyper__HyperRep_sqrts1 ok
test_sympy__functions__special__hyper__HyperRep_sqrts2 ok
test_sympy__functions__special__hyper__HyperRep_log2 ok
test_sympy__functions__special__hyper__HyperRep_cosasin ok
test_sympy__functions__special__hyper__HyperRep_sinasin ok
test_sympy__functions__special__hyper__appellf1 ok
test_sympy__functions__special__polynomials__OrthogonalPolynomial abstract class
s
test_sympy__functions__special__polynomials__jacobi ok
test_sympy__functions__special__polynomials__gegenbauer ok
test_sympy__functions__special__polynomials__chebyshevt ok
test_sympy__functions__special__polynomials__chebyshevt_root ok
test_sympy__functions__special__polynomials__chebyshevu ok
test_sympy__functions__special__polynomials__chebyshevu_root ok
test_sympy__functions__special__polynomials__hermite ok
test_sympy__functions__special__polynomials__legendre ok
test_sympy__functions__special__polynomials__assoc_legendre ok
test_sympy__functions__special__polynomials__laguerre ok
test_sympy__functions__special__polynomials__assoc_laguerre ok
test_sympy__functions__special__spherical_harmonics__Ynm ok
test_sympy__functions__special__spherical_harmonics__Znm ok
test_sympy__functions__special__tensor_functions__LeviCivita ok
test_sympy__functions__special__tensor_functions__KroneckerDelta ok
test_sympy__functions__special__zeta_functions__dirichlet_eta ok
test_sympy__functions__special__zeta_functions__zeta ok
test_sympy__functions__special__zeta_functions__lerchphi ok
test_sympy__functions__special__zeta_functions__polylog ok
test_sympy__functions__special__zeta_functions__stieltjes ok
test_sympy__integrals__integrals__Integral ok
test_sympy__integrals__risch__NonElementaryIntegral ok
test_sympy__integrals__transforms__IntegralTransform abstract class s
test_sympy__integrals__transforms__MellinTransform ok
test_sympy__integrals__transforms__InverseMellinTransform ok
test_sympy__integrals__transforms__LaplaceTransform ok
test_sympy__integrals__transforms__InverseLaplaceTransform ok
test_sympy__integrals__transforms__FourierTypeTransform abstract class s
test_sympy__integrals__transforms__InverseFourierTransform ok
test_sympy__integrals__transforms__FourierTransform ok
test_sympy__integrals__transforms__SineCosineTypeTransform abstract class s
test_sympy__integrals__transforms__InverseSineTransform ok
test_sympy__integrals__transforms__SineTransform ok
test_sympy__integrals__transforms__InverseCosineTransform ok
test_sympy__integrals__transforms__CosineTransform ok
test_sympy__integrals__transforms__HankelTypeTransform abstract class s
test_sympy__integrals__transforms__InverseHankelTransform ok
test_sympy__integrals__transforms__HankelTransform ok
test_sympy__liealgebras__cartan_type__CartanType_generator f
test_sympy__liealgebras__cartan_type__Standard_Cartan f
test_sympy__liealgebras__weyl_group__WeylGroup f
test_sympy__liealgebras__root_system__RootSystem f
test_sympy__liealgebras__type_a__TypeA f
test_sympy__liealgebras__type_b__TypeB f
test_sympy__liealgebras__type_c__TypeC f
test_sympy__liealgebras__type_d__TypeD f
test_sympy__liealgebras__type_e__TypeE f
test_sympy__liealgebras__type_f__TypeF f
test_sympy__liealgebras__type_g__TypeG f
test_sympy__logic__boolalg__And ok
test_sympy__logic__boolalg__Boolean abstract class s
test_sympy__logic__boolalg__BooleanFunction ok
test_sympy__logic__boolalg__BooleanAtom abstract class s
test_sympy__logic__boolalg__BooleanTrue ok
test_sympy__logic__boolalg__BooleanFalse ok
test_sympy__logic__boolalg__Equivalent ok
test_sympy__logic__boolalg__ITE ok
test_sympy__logic__boolalg__Implies ok
test_sympy__logic__boolalg__Nand ok
test_sympy__logic__boolalg__Nor ok
test_sympy__logic__boolalg__Not ok
test_sympy__logic__boolalg__Or ok
test_sympy__logic__boolalg__Xor ok
test_sympy__logic__boolalg__Xnor ok
test_sympy__matrices__matrices__DeferredVector ok
test_sympy__matrices__expressions__matexpr__MatrixBase abstract class s
test_sympy__matrices__immutable__ImmutableDenseMatrix ok
test_sympy__matrices__immutable__ImmutableSparseMatrix ok
test_sympy__matrices__expressions__slice__MatrixSlice ok
test_sympy__matrices__expressions__applyfunc__ElementwiseApplyFunction ok
test_sympy__matrices__expressions__blockmatrix__BlockDiagMatrix ok
test_sympy__matrices__expressions__blockmatrix__BlockMatrix ok
test_sympy__matrices__expressions__inverse__Inverse ok
test_sympy__matrices__expressions__matadd__MatAdd ok
test_sympy__matrices__expressions__matexpr__Identity ok
test_sympy__matrices__expressions__matexpr__GenericIdentity ok
test_sympy__matrices__expressions__matexpr__MatrixExpr abstract class s
test_sympy__matrices__expressions__matexpr__MatrixElement ok
test_sympy__matrices__expressions__matexpr__MatrixSymbol ok
test_sympy__matrices__expressions__matexpr__ZeroMatrix ok
test_sympy__matrices__expressions__matexpr__OneMatrix ok
test_sympy__matrices__expressions__matexpr__GenericZeroMatrix ok
test_sympy__matrices__expressions__matmul__MatMul ok
test_sympy__matrices__expressions__dotproduct__DotProduct ok
test_sympy__matrices__expressions__diagonal__DiagonalMatrix ok
test_sympy__matrices__expressions__diagonal__DiagonalOf ok
test_sympy__matrices__expressions__diagonal__DiagonalizeVector ok
test_sympy__matrices__expressions__hadamard__HadamardProduct ok
test_sympy__matrices__expressions__hadamard__HadamardPower ok
test_sympy__matrices__expressions__kronecker__KroneckerProduct ok
test_sympy__matrices__expressions__matpow__MatPow ok
test_sympy__matrices__expressions__transpose__Transpose ok
test_sympy__matrices__expressions__adjoint__Adjoint ok
test_sympy__matrices__expressions__trace__Trace ok
test_sympy__matrices__expressions__determinant__Determinant ok
test_sympy__matrices__expressions__funcmatrix__FunctionMatrix ok
test_sympy__matrices__expressions__fourier__DFT ok
test_sympy__matrices__expressions__fourier__IDFT ok
test_sympy__matrices__expressions__factorizations__LofLU ok
test_sympy__matrices__expressions__factorizations__UofLU ok
test_sympy__matrices__expressions__factorizations__QofQR ok
test_sympy__matrices__expressions__factorizations__RofQR ok
test_sympy__matrices__expressions__factorizations__LofCholesky ok
test_sympy__matrices__expressions__factorizations__UofCholesky ok
test_sympy__matrices__expressions__factorizations__EigenVectors ok
test_sympy__matrices__expressions__factorizations__EigenValues ok
test_sympy__matrices__expressions__factorizations__UofSVD ok
test_sympy__matrices__expressions__factorizations__VofSVD ok
test_sympy__matrices__expressions__factorizations__SofSVD ok
test_sympy__matrices__expressions__factorizations__Factorization abstract class
s
test_sympy__physics__vector__frame__CoordinateSym ok
test_sympy__physics__paulialgebra__Pauli ok
test_sympy__physics__quantum__anticommutator__AntiCommutator ok
test_sympy__physics__quantum__cartesian__PositionBra3D ok
test_sympy__physics__quantum__cartesian__PositionKet3D ok
test_sympy__physics__quantum__cartesian__PositionState3D ok
test_sympy__physics__quantum__cartesian__PxBra ok
test_sympy__physics__quantum__cartesian__PxKet ok
test_sympy__physics__quantum__cartesian__PxOp ok
test_sympy__physics__quantum__cartesian__XBra ok
test_sympy__physics__quantum__cartesian__XKet ok
test_sympy__physics__quantum__cartesian__XOp ok
test_sympy__physics__quantum__cartesian__YOp ok
test_sympy__physics__quantum__cartesian__ZOp ok
test_sympy__physics__quantum__cg__CG ok
test_sympy__physics__quantum__cg__Wigner3j ok
test_sympy__physics__quantum__cg__Wigner6j ok
test_sympy__physics__quantum__cg__Wigner9j ok
test_sympy__physics__quantum__circuitplot__Mz ok
test_sympy__physics__quantum__circuitplot__Mx ok
test_sympy__physics__quantum__commutator__Commutator ok
test_sympy__physics__quantum__constants__HBar ok
test_sympy__physics__quantum__dagger__Dagger ok
test_sympy__physics__quantum__gate__CGate ok
test_sympy__physics__quantum__gate__CGateS ok
test_sympy__physics__quantum__gate__CNotGate ok
test_sympy__physics__quantum__gate__Gate ok
test_sympy__physics__quantum__gate__HadamardGate ok
test_sympy__physics__quantum__gate__IdentityGate ok
test_sympy__physics__quantum__gate__OneQubitGate ok
test_sympy__physics__quantum__gate__PhaseGate ok
test_sympy__physics__quantum__gate__SwapGate ok
test_sympy__physics__quantum__gate__TGate ok
test_sympy__physics__quantum__gate__TwoQubitGate ok
test_sympy__physics__quantum__gate__UGate ok
test_sympy__physics__quantum__gate__XGate ok
test_sympy__physics__quantum__gate__YGate ok
test_sympy__physics__quantum__gate__ZGate ok
test_sympy__physics__quantum__grover__OracleGate TODO: sympy.physics s
test_sympy__physics__quantum__grover__WGate ok
test_sympy__physics__quantum__hilbert__ComplexSpace ok
test_sympy__physics__quantum__hilbert__DirectSumHilbertSpace ok
test_sympy__physics__quantum__hilbert__FockSpace ok
test_sympy__physics__quantum__hilbert__HilbertSpace ok
test_sympy__physics__quantum__hilbert__L2 ok
test_sympy__physics__quantum__hilbert__TensorPowerHilbertSpace ok
test_sympy__physics__quantum__hilbert__TensorProductHilbertSpace ok
test_sympy__physics__quantum__innerproduct__InnerProduct ok
test_sympy__physics__quantum__operator__DifferentialOperator ok
test_sympy__physics__quantum__operator__HermitianOperator ok
test_sympy__physics__quantum__operator__IdentityOperator ok
test_sympy__physics__quantum__operator__Operator ok
test_sympy__physics__quantum__operator__OuterProduct ok
test_sympy__physics__quantum__operator__UnitaryOperator ok
test_sympy__physics__quantum__piab__PIABBra ok
test_sympy__physics__quantum__boson__BosonOp ok
test_sympy__physics__quantum__boson__BosonFockKet ok
test_sympy__physics__quantum__boson__BosonFockBra ok
test_sympy__physics__quantum__boson__BosonCoherentKet ok
test_sympy__physics__quantum__boson__BosonCoherentBra ok
test_sympy__physics__quantum__fermion__FermionOp ok
test_sympy__physics__quantum__fermion__FermionFockKet ok
test_sympy__physics__quantum__fermion__FermionFockBra ok
test_sympy__physics__quantum__pauli__SigmaOpBase ok
test_sympy__physics__quantum__pauli__SigmaX ok
test_sympy__physics__quantum__pauli__SigmaY ok
test_sympy__physics__quantum__pauli__SigmaZ ok
test_sympy__physics__quantum__pauli__SigmaMinus ok
test_sympy__physics__quantum__pauli__SigmaPlus ok
test_sympy__physics__quantum__pauli__SigmaZKet ok
test_sympy__physics__quantum__pauli__SigmaZBra ok
test_sympy__physics__quantum__piab__PIABHamiltonian ok
test_sympy__physics__quantum__piab__PIABKet ok
test_sympy__physics__quantum__qexpr__QExpr ok
test_sympy__physics__quantum__qft__Fourier ok
test_sympy__physics__quantum__qft__IQFT ok
test_sympy__physics__quantum__qft__QFT ok
test_sympy__physics__quantum__qft__RkGate ok
test_sympy__physics__quantum__qubit__IntQubit ok
test_sympy__physics__quantum__qubit__IntQubitBra ok
test_sympy__physics__quantum__qubit__IntQubitState ok
test_sympy__physics__quantum__qubit__Qubit ok
test_sympy__physics__quantum__qubit__QubitBra ok
test_sympy__physics__quantum__qubit__QubitState ok
test_sympy__physics__quantum__density__Density ok
test_sympy__physics__quantum__shor__CMod TODO: sympy.physics.quantum.shor: Cmod Not Implemented
s
test_sympy__physics__quantum__spin__CoupledSpinState ok
test_sympy__physics__quantum__spin__J2Op ok
test_sympy__physics__quantum__spin__JminusOp ok
test_sympy__physics__quantum__spin__JplusOp ok
test_sympy__physics__quantum__spin__JxBra ok
test_sympy__physics__quantum__spin__JxBraCoupled ok
test_sympy__physics__quantum__spin__JxKet ok
test_sympy__physics__quantum__spin__JxKetCoupled ok
test_sympy__physics__quantum__spin__JxOp ok
test_sympy__physics__quantum__spin__JyBra ok
test_sympy__physics__quantum__spin__JyBraCoupled ok
test_sympy__physics__quantum__spin__JyKet ok
test_sympy__physics__quantum__spin__JyKetCoupled ok
test_sympy__physics__quantum__spin__JyOp ok
test_sympy__physics__quantum__spin__JzBra ok
test_sympy__physics__quantum__spin__JzBraCoupled ok
test_sympy__physics__quantum__spin__JzKet ok
test_sympy__physics__quantum__spin__JzKetCoupled ok
test_sympy__physics__quantum__spin__JzOp ok
test_sympy__physics__quantum__spin__Rotation ok
test_sympy__physics__quantum__spin__SpinState ok
test_sympy__physics__quantum__spin__WignerD ok
test_sympy__physics__quantum__state__Bra ok
test_sympy__physics__quantum__state__BraBase ok
test_sympy__physics__quantum__state__Ket ok
test_sympy__physics__quantum__state__KetBase ok
test_sympy__physics__quantum__state__State ok
test_sympy__physics__quantum__state__StateBase ok
test_sympy__physics__quantum__state__TimeDepBra ok
test_sympy__physics__quantum__state__TimeDepKet ok
test_sympy__physics__quantum__state__TimeDepState ok
test_sympy__physics__quantum__state__Wavefunction ok
test_sympy__physics__quantum__tensorproduct__TensorProduct ok
test_sympy__physics__quantum__identitysearch__GateIdentity ok
test_sympy__physics__quantum__sho1d__SHOOp ok
test_sympy__physics__quantum__sho1d__RaisingOp ok
test_sympy__physics__quantum__sho1d__LoweringOp ok
test_sympy__physics__quantum__sho1d__NumberOp ok
test_sympy__physics__quantum__sho1d__Hamiltonian ok
test_sympy__physics__quantum__sho1d__SHOState ok
test_sympy__physics__quantum__sho1d__SHOKet ok
test_sympy__physics__quantum__sho1d__SHOBra ok
test_sympy__physics__secondquant__AnnihilateBoson ok
test_sympy__physics__secondquant__AnnihilateFermion ok
test_sympy__physics__secondquant__Annihilator abstract class s
test_sympy__physics__secondquant__AntiSymmetricTensor ok
test_sympy__physics__secondquant__BosonState ok
test_sympy__physics__secondquant__BosonicOperator abstract class s
test_sympy__physics__secondquant__Commutator ok
test_sympy__physics__secondquant__CreateBoson ok
test_sympy__physics__secondquant__CreateFermion ok
test_sympy__physics__secondquant__Creator abstract class s
test_sympy__physics__secondquant__Dagger ok
test_sympy__physics__secondquant__FermionState ok
test_sympy__physics__secondquant__FermionicOperator ok
test_sympy__physics__secondquant__FockState ok
test_sympy__physics__secondquant__FockStateBosonBra ok
test_sympy__physics__secondquant__FockStateBosonKet ok
test_sympy__physics__secondquant__FockStateBra ok
test_sympy__physics__secondquant__FockStateFermionBra ok
test_sympy__physics__secondquant__FockStateFermionKet ok
test_sympy__physics__secondquant__FockStateKet ok
test_sympy__physics__secondquant__InnerProduct ok
test_sympy__physics__secondquant__NO ok
test_sympy__physics__secondquant__PermutationOperator ok
test_sympy__physics__secondquant__SqOperator ok
test_sympy__physics__secondquant__TensorSymbol ok
test_sympy__physics__units__dimensions__Dimension ok
test_sympy__physics__units__dimensions__DimensionSystem ok
test_sympy__physics__units__quantities__Quantity ok
test_sympy__physics__units__prefixes__Prefix ok
test_sympy__core__numbers__AlgebraicNumber ok
test_sympy__polys__polytools__GroebnerBasis ok
test_sympy__polys__polytools__Poly ok
test_sympy__polys__polytools__PurePoly ok
test_sympy__polys__rootoftools__RootOf abstract class s
test_sympy__polys__rootoftools__ComplexRootOf ok
test_sympy__polys__rootoftools__RootSum ok
test_sympy__series__limits__Limit ok
test_sympy__series__order__Order ok
test_sympy__series__sequences__SeqBase Abstract Class s
test_sympy__series__sequences__EmptySequence ok
test_sympy__series__sequences__SeqExpr Abstract Class s
test_sympy__series__sequences__SeqPer ok
test_sympy__series__sequences__SeqFormula ok
test_sympy__series__sequences__RecursiveSeq ok
test_sympy__series__sequences__SeqExprOp ok
test_sympy__series__sequences__SeqAdd ok
test_sympy__series__sequences__SeqMul ok
test_sympy__series__series_class__SeriesBase Abstract Class s
test_sympy__series__fourier__FourierSeries ok
test_sympy__series__fourier__FiniteFourierSeries ok
test_sympy__series__formal__FormalPowerSeries ok
test_sympy__series__formal__Coeff ok
test_sympy__simplify__hyperexpand__Hyper_Function ok
test_sympy__simplify__hyperexpand__G_Function ok
test_sympy__tensor__array__ndim_array__ImmutableNDimArray abstract class s
test_sympy__tensor__array__dense_ndim_array__ImmutableDenseNDimArray ok
test_sympy__tensor__array__sparse_ndim_array__ImmutableSparseNDimArray ok
test_sympy__tensor__array__array_comprehension__ArrayComprehension ok
test_sympy__tensor__functions__TensorProduct ok
test_sympy__tensor__indexed__Idx ok
test_sympy__tensor__indexed__Indexed ok
test_sympy__tensor__indexed__IndexedBase ok
test_sympy__tensor__tensor__TensorIndexType ok
test_sympy__tensor__tensor__TensorSymmetry ok
test_sympy__tensor__tensor__TensorType ok
test_sympy__tensor__tensor__TensorHead ok
test_sympy__tensor__tensor__TensorIndex ok
test_sympy__tensor__tensor__TensExpr abstract class s
test_sympy__tensor__tensor__TensAdd ok
test_sympy__tensor__tensor__Tensor ok
test_sympy__tensor__tensor__TensMul ok
test_sympy__tensor__tensor__TensorElement ok
test_sympy__tensor__toperators__PartialDerivative ok
test_as_coeff_add ok
test_sympy__geometry__curve__Curve ok
test_sympy__geometry__point__Point ok
test_sympy__geometry__point__Point2D ok
test_sympy__geometry__point__Point3D ok
test_sympy__geometry__ellipse__Ellipse ok
test_sympy__geometry__ellipse__Circle ok
test_sympy__geometry__parabola__Parabola ok
test_sympy__geometry__line__LinearEntity abstract class s
test_sympy__geometry__line__Line ok
test_sympy__geometry__line__Ray ok
test_sympy__geometry__line__Segment ok
test_sympy__geometry__line__LinearEntity2D abstract class s
test_sympy__geometry__line__Line2D ok
test_sympy__geometry__line__Ray2D ok
test_sympy__geometry__line__Segment2D ok
test_sympy__geometry__line__LinearEntity3D abstract class s
test_sympy__geometry__line__Line3D ok
test_sympy__geometry__line__Segment3D ok
test_sympy__geometry__line__Ray3D ok
test_sympy__geometry__plane__Plane ok
test_sympy__geometry__polygon__Polygon ok
test_sympy__geometry__polygon__RegularPolygon ok
test_sympy__geometry__polygon__Triangle ok
test_sympy__geometry__entity__GeometryEntity ok
test_sympy__geometry__entity__GeometrySet abstract class s
test_sympy__diffgeom__diffgeom__Manifold ok
test_sympy__diffgeom__diffgeom__Patch ok
test_sympy__diffgeom__diffgeom__CoordSystem ok
test_sympy__diffgeom__diffgeom__Point f
test_sympy__diffgeom__diffgeom__BaseScalarField ok
test_sympy__diffgeom__diffgeom__BaseVectorField ok
test_sympy__diffgeom__diffgeom__Differential ok
test_sympy__diffgeom__diffgeom__Commutator ok
test_sympy__diffgeom__diffgeom__TensorProduct ok
test_sympy__diffgeom__diffgeom__WedgeProduct ok
test_sympy__diffgeom__diffgeom__LieDerivative ok
test_sympy__diffgeom__diffgeom__BaseCovarDerivativeOp f
test_sympy__diffgeom__diffgeom__CovarDerivativeOp ok
test_sympy__categories__baseclasses__Class ok
test_sympy__categories__baseclasses__Object ok
test_sympy__categories__baseclasses__Morphism f
test_sympy__categories__baseclasses__IdentityMorphism ok
test_sympy__categories__baseclasses__NamedMorphism ok
test_sympy__categories__baseclasses__CompositeMorphism ok
test_sympy__categories__baseclasses__Diagram ok
test_sympy__categories__baseclasses__Category ok
test_sympy__ntheory__factor___totient ok
test_sympy__ntheory__factor___reduced_totient ok
test_sympy__ntheory__factor___divisor_sigma ok
test_sympy__ntheory__factor___udivisor_sigma ok
test_sympy__ntheory__factor___primenu ok
test_sympy__ntheory__factor___primeomega ok
test_sympy__ntheory__residue_ntheory__mobius ok
test_sympy__ntheory__generate__primepi ok
test_sympy__physics__optics__waves__TWave ok
test_sympy__physics__optics__gaussopt__BeamParameter ok
test_sympy__physics__optics__medium__Medium ok
test_sympy__codegen__array_utils__CodegenArrayContraction ok
test_sympy__codegen__array_utils__CodegenArrayDiagonal ok
test_sympy__codegen__array_utils__CodegenArrayTensorProduct ok
test_sympy__codegen__array_utils__CodegenArrayElementwiseAdd ok
test_sympy__codegen__array_utils__CodegenArrayPermuteDims ok
test_sympy__codegen__ast__Assignment ok
test_sympy__codegen__cfunctions__expm1 ok
test_sympy__codegen__cfunctions__log1p ok
test_sympy__codegen__cfunctions__exp2 ok
test_sympy__codegen__cfunctions__log2 ok
test_sympy__codegen__cfunctions__fma ok
test_sympy__codegen__cfunctions__log10 ok
test_sympy__codegen__cfunctions__Sqrt ok
test_sympy__codegen__cfunctions__Cbrt ok
test_sympy__codegen__cfunctions__hypot ok
test_sympy__codegen__fnodes__FFunction ok
test_sympy__codegen__fnodes__F95Function ok
test_sympy__codegen__fnodes__isign ok
test_sympy__codegen__fnodes__dsign ok
test_sympy__codegen__fnodes__cmplx ok
test_sympy__codegen__fnodes__kind ok
test_sympy__codegen__fnodes__merge ok
test_sympy__codegen__fnodes___literal ok
test_sympy__codegen__fnodes__literal_sp ok
test_sympy__codegen__fnodes__literal_dp ok
test_sympy__vector__coordsysrect__CoordSys3D ok
test_sympy__vector__point__Point ok
test_sympy__vector__basisdependent__BasisDependent ok
test_sympy__vector__basisdependent__BasisDependentMul ok
test_sympy__vector__basisdependent__BasisDependentAdd ok
test_sympy__vector__basisdependent__BasisDependentZero ok
test_sympy__vector__vector__BaseVector ok
test_sympy__vector__vector__VectorAdd ok
test_sympy__vector__vector__VectorMul ok
test_sympy__vector__vector__VectorZero ok
test_sympy__vector__vector__Vector ok
test_sympy__vector__vector__Cross ok
test_sympy__vector__vector__Dot ok
test_sympy__vector__dyadic__Dyadic ok
test_sympy__vector__dyadic__BaseDyadic ok
test_sympy__vector__dyadic__DyadicMul ok
test_sympy__vector__dyadic__DyadicAdd ok
test_sympy__vector__dyadic__DyadicZero ok
test_sympy__vector__deloperator__Del ok
test_sympy__vector__operators__Curl ok
test_sympy__vector__operators__Laplacian ok
test_sympy__vector__operators__Divergence ok
test_sympy__vector__operators__Gradient ok
test_sympy__vector__orienters__Orienter ok
test_sympy__vector__orienters__ThreeAngleOrienter ok
test_sympy__vector__orienters__AxisOrienter ok
test_sympy__vector__orienters__BodyOrienter ok
test_sympy__vector__orienters__SpaceOrienter ok
test_sympy__vector__orienters__QuaternionOrienter ok
test_sympy__vector__scalar__BaseScalar ok
test_sympy__physics__wigner__Wigner3j ok
test_sympy__integrals__rubi__symbol__matchpyWC ok
test_sympy__integrals__rubi__utility_function__rubi_unevaluated_expr ok
test_sympy__integrals__rubi__utility_function__rubi_exp ok
test_sympy__integrals__rubi__utility_function__rubi_log ok
test_sympy__integrals__rubi__utility_function__Int ok
test_sympy__integrals__rubi__utility_function__Util_Coefficient ok
test_sympy__integrals__rubi__utility_function__Gamma ok
test_sympy__integrals__rubi__utility_function__Util_Part ok
test_sympy__integrals__rubi__utility_function__PolyGamma ok
test_sympy__integrals__rubi__utility_function__ProductLog ok                [OK]

sympy/sets/tests/test_sets.py[78]
test_imageset F
test_interval_arguments ok
test_interval_symbolic_end_points ok
test_union ok
test_union_iter ok
test_difference ok
test_Complement ok
test_complement ok
test_intersect1 ok
test_intersection ok
test_issue_9623 ok
test_is_disjoint ok
test_ProductSet_of_single_arg_is_arg ok
test_interval_subs ok
test_interval_to_mpi ok
test_measure ok
test_is_subset ok
test_is_proper_subset ok
test_is_superset ok
test_is_proper_superset ok
test_contains ok
test_interval_symbolic ok
test_union_contains ok
test_is_number ok
test_Interval_is_left_unbounded ok
test_Interval_is_right_unbounded ok
test_Interval_as_relational ok
test_Finite_as_relational ok
test_Union_as_relational ok
test_Intersection_as_relational ok
test_EmptySet ok
test_finite_basic ok
test_powerset ok
test_product_basic ok
test_real ok
test_supinf ok
test_universalset ok
test_Union_of_ProductSets_shares ok
test_Interval_free_symbols ok
test_image_interval ok
test_image_piecewise ok
test_image_Intersection f
test_image_FiniteSet ok
test_image_Union ok
test_image_EmptySet ok
test_issue_5724_7680 ok
test_boundary ok
test_boundary_Union ok
test_union_boundary_of_joining_sets f
test_boundary_ProductSet ok
test_boundary_ProductSet_line ok
test_is_open ok
test_is_closed ok
test_closure ok
test_interior ok
test_issue_7841 ok
test_Eq ok
test_SymmetricDifference ok
test_issue_9536 ok
test_issue_9637 ok
test_issue_9808 f
test_issue_9956 ok
test_issue_Symbol_inter ok
test_issue_11827 ok
test_issue_10113 ok
test_issue_10248 ok
test_issue_9447 ok
test_issue_10337 ok
test_issue_10326 ok
test_issue_2799 ok
test_issue_9706 ok
test_issue_8257 ok
test_issue_10931 ok
test_issue_11174 ok
test_finite_set_intersection ok
test_union_intersection_constructor ok
test_Union_contains ok
test_issue_16878b f                                                       [FAIL]


________________________________ slowest tests _________________________________
test_image_interval - Took 47.241 seconds
test_issue_10113 - Took 67.067 seconds
________________________________________________________________________________
_________________ sympy/sets/tests/test_sets.py:test_imageset __________________
Traceback (most recent call last):
  File "/testbed/sympy/sets/tests/test_sets.py", line 24, in test_imageset
    assert (1, r) not in imageset(x, (x, x), S.Reals)
AssertionError

 tests finished: 854 passed, 1 failed, 63 skipped, 22 expected to fail,
in 131.95 seconds
DO *NOT* COMMIT!

