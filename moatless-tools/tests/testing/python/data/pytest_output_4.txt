Internet access disabled
============================= test session starts ==============================
platform linux -- Python 3.6.13, pytest-3.3.1, py-1.11.0, pluggy-0.6.0 -- /opt/miniconda3/envs/testbed/bin/python
cachedir: .

Running tests with Astropy version 3.1.dev22336.
Running tests in astropy/wcs/tests/test_wcs.py.

Date: 2024-08-30T05:49:01

Platform: Linux-5.15.0-1070-azure-x86_64-with-debian-bookworm-sid

Executable: /opt/miniconda3/envs/testbed/bin/python

Full Python Version:
3.6.13 |Anaconda, Inc.| (default, Jun  4 2021, 14:25:59)
[GCC 7.5.0]

encodings: sys: utf-8, locale: ANSI_X3.4-1968, filesystem: ascii
byteorder: little
float info: dig: 15, mant_dig: 15

Numpy: 1.16.0
Scipy: not available
Matplotlib: not available
h5py: not available
Pandas: not available
Cython: 0.27.3
astropy_helpers: 3.1.dev957
Using Astropy options: remote_data: none.

rootdir: /testbed, inifile: setup.cfg
plugins: xdist-1.20.1, remotedata-0.2.0, openfiles-0.2.0, mock-1.6.3, forked-0.2, filter-subpackage-0.1, doctestplus-0.1.2, cov-2.5.1, astropy-header-0.1, arraydiff-0.1, hypothesis-3.44.2
collecting ... collected 56 items

astropy/wcs/tests/test_wcs.py::TestMaps::test_consistency PASSED
astropy/wcs/tests/test_wcs.py::TestMaps::test_maps PASSED
astropy/wcs/tests/test_wcs.py::TestSpectra::test_consistency PASSED
astropy/wcs/tests/test_wcs.py::TestSpectra::test_spectra PASSED
astropy/wcs/tests/test_wcs.py::test_fixes PASSED
astropy/wcs/tests/test_wcs.py::test_outside_sky PASSED
astropy/wcs/tests/test_wcs.py::test_pix2world PASSED
astropy/wcs/tests/test_wcs.py::test_load_fits_path PASSED
astropy/wcs/tests/test_wcs.py::test_dict_init PASSED
astropy/wcs/tests/test_wcs.py::test_extra_kwarg PASSED
astropy/wcs/tests/test_wcs.py::test_3d_shapes PASSED
astropy/wcs/tests/test_wcs.py::test_preserve_shape PASSED
astropy/wcs/tests/test_wcs.py::test_broadcasting PASSED
astropy/wcs/tests/test_wcs.py::test_shape_mismatch PASSED
astropy/wcs/tests/test_wcs.py::test_invalid_shape PASSED
astropy/wcs/tests/test_wcs.py::test_warning_about_defunct_keywords PASSED
astropy/wcs/tests/test_wcs.py::test_warning_about_defunct_keywords_exception PASSED
astropy/wcs/tests/test_wcs.py::test_to_header_string PASSED
astropy/wcs/tests/test_wcs.py::test_to_fits PASSED
astropy/wcs/tests/test_wcs.py::test_to_header_warning PASSED
astropy/wcs/tests/test_wcs.py::test_no_comments_in_header PASSED
astropy/wcs/tests/test_wcs.py::test_find_all_wcs_crash PASSED
astropy/wcs/tests/test_wcs.py::test_validate PASSED
astropy/wcs/tests/test_wcs.py::test_validate_with_2_wcses PASSED
astropy/wcs/tests/test_wcs.py::test_crpix_maps_to_crval PASSED
astropy/wcs/tests/test_wcs.py::test_all_world2pix PASSED
astropy/wcs/tests/test_wcs.py::test_scamp_sip_distortion_parameters PASSED
astropy/wcs/tests/test_wcs.py::test_fixes2 PASSED
astropy/wcs/tests/test_wcs.py::test_unit_normalization PASSED
astropy/wcs/tests/test_wcs.py::test_footprint_to_file PASSED
astropy/wcs/tests/test_wcs.py::test_validate_faulty_wcs PASSED
astropy/wcs/tests/test_wcs.py::test_error_message PASSED
astropy/wcs/tests/test_wcs.py::test_out_of_bounds PASSED
astropy/wcs/tests/test_wcs.py::test_calc_footprint_1 PASSED
astropy/wcs/tests/test_wcs.py::test_calc_footprint_2 PASSED
astropy/wcs/tests/test_wcs.py::test_calc_footprint_3 PASSED
astropy/wcs/tests/test_wcs.py::test_sip PASSED
astropy/wcs/tests/test_wcs.py::test_printwcs PASSED
astropy/wcs/tests/test_wcs.py::test_invalid_spherical PASSED
astropy/wcs/tests/test_wcs.py::test_no_iteration PASSED
astropy/wcs/tests/test_wcs.py::test_sip_tpv_agreement PASSED
astropy/wcs/tests/test_wcs.py::test_tpv_copy PASSED
astropy/wcs/tests/test_wcs.py::test_hst_wcs PASSED
astropy/wcs/tests/test_wcs.py::test_list_naxis PASSED
astropy/wcs/tests/test_wcs.py::test_sip_broken PASSED
astropy/wcs/tests/test_wcs.py::test_no_truncate_crval PASSED
astropy/wcs/tests/test_wcs.py::test_no_truncate_crval_try2 PASSED
astropy/wcs/tests/test_wcs.py::test_no_truncate_crval_p17 PASSED
astropy/wcs/tests/test_wcs.py::test_no_truncate_using_compare PASSED
astropy/wcs/tests/test_wcs.py::test_passing_ImageHDU PASSED
astropy/wcs/tests/test_wcs.py::test_inconsistent_sip PASSED
astropy/wcs/tests/test_wcs.py::test_bounds_check PASSED
astropy/wcs/tests/test_wcs.py::test_naxis PASSED
astropy/wcs/tests/test_wcs.py::test_sip_with_altkey PASSED
astropy/wcs/tests/test_wcs.py::test_to_fits_1 PASSED
astropy/wcs/tests/test_wcs.py::test_keyedsip PASSED

=============================== warnings summary ===============================
None
  Module already imported so cannot be rewritten: astropy.tests.plugins.display

-- Docs: http://doc.pytest.org/en/latest/warnings.html
==================== 56 passed, 1 warnings in 1.55 seconds =====================
