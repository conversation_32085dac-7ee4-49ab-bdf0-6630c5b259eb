/testbed/sympy/core/basic.py:3: DeprecationWarning: Using or importing the ABCs from 'collections' instead of from 'collections.abc' is deprecated since Python 3.3, and in 3.10 it will stop working
  from collections import Mapping, defaultdict
/testbed/sympy/core/expr.py:12: DeprecationWarning: Using or importing the ABCs from 'collections' instead of from 'collections.abc' is deprecated since Python 3.3, and in 3.10 it will stop working
  from collections import defaultdict, Iterable
/testbed/sympy/core/containers.py:271: DeprecationWarning: Using or importing the ABCs from 'collections' instead of from 'collections.abc' is deprecated since Python 3.3, and in 3.10 it will stop working
  class OrderedSet(collections.MutableSet):
/testbed/sympy/plotting/plot.py:28: DeprecationWarning: Using or importing the ABCs from 'collections' instead of from 'collections.abc' is deprecated since Python 3.3, and in 3.10 it will stop working
  from collections import Callable
/testbed/sympy/core/basic.py:3: DeprecationWarning: Using or importing the ABCs from 'collections' instead of from 'collections.abc' is deprecated since Python 3.3, and in 3.10 it will stop working
  from collections import Mapping, defaultdict
/testbed/sympy/core/expr.py:12: DeprecationWarning: Using or importing the ABCs from 'collections' instead of from 'collections.abc' is deprecated since Python 3.3, and in 3.10 it will stop working
  from collections import defaultdict, Iterable
/testbed/sympy/core/containers.py:271: DeprecationWarning: Using or importing the ABCs from 'collections' instead of from 'collections.abc' is deprecated since Python 3.3, and in 3.10 it will stop working
  class OrderedSet(collections.MutableSet):
/testbed/sympy/plotting/plot.py:28: DeprecationWarning: Using or importing the ABCs from 'collections' instead of from 'collections.abc' is deprecated since Python 3.3, and in 3.10 it will stop working
  from collections import Callable
============================= test process starts ==============================
executable:         /opt/miniconda3/envs/testbed/bin/python  (3.9.19-final-0) [CPython]
architecture:       64-bit
cache:              no
ground types:       python
numpy:              None
random seed:        2560686
hash randomization: on (PYTHONHASHSEED=1113233383)

sympy/printing/pretty/tests/test_pretty.py[117]
test_pretty_ascii_str ok
test_pretty_unicode_str ok
test_upretty_greek ok
test_upretty_multiindex ok
test_upretty_sub_super ok
test_upretty_subs_missing_in_24 ok
test_missing_in_2X_issue_9047 X
test_upretty_modifiers ok
test_pretty_Cycle ok
test_pretty_basic ok
test_negative_fractions ok
test_issue_5524 ok
test_pretty_ordering ok
test_EulerGamma ok
test_GoldenRatio ok
test_pretty_relational ok
test_Assignment ok
test_AugmentedAssignment ok
test_issue_7117 ok
test_pretty_rational ok
test_pretty_functions ok
test_pretty_sqrt ok
test_pretty_sqrt_char_knob ok
test_pretty_sqrt_longsymbol_no_sqrt_char ok
test_pretty_KroneckerDelta ok
test_pretty_product ok
test_pretty_lambda ok
test_pretty_order ok
test_pretty_derivatives ok
test_pretty_integrals ok
test_pretty_matrix ok
test_pretty_ndim_arrays ok
test_tensor_TensorProduct ok
test_diffgeom_print_WedgeProduct ok
test_Adjoint ok
test_pretty_Trace_issue_9044 ok
test_MatrixExpressions ok
test_pretty_dotproduct ok
test_pretty_piecewise ok
test_pretty_ITE ok
test_pretty_seq ok
test_any_object_in_sequence ok
test_print_builtin_set ok
test_pretty_sets ok
test_pretty_SetExpr ok
test_pretty_ImageSet ok
test_pretty_ConditionSet ok
test_pretty_ComplexRegion ok
test_pretty_Union_issue_10414 ok
test_pretty_Intersection_issue_10414 ok
test_ProductSet_paranthesis ok
test_ProductSet_prod_char_issue_10413 ok
test_pretty_sequences ok
test_pretty_FourierSeries ok
test_pretty_FormalPowerSeries ok
test_pretty_limits ok
test_pretty_ComplexRootOf ok
test_pretty_RootSum ok
test_GroebnerBasis ok
test_pretty_Boolean ok
test_pretty_Domain ok
test_pretty_prec ok
test_pprint ok
test_pretty_class ok
test_pretty_no_wrap_line ok
test_settings ok
test_pretty_sum ok
test_units ok
test_pretty_Subs ok
test_gammas ok
test_beta ok
test_function_subclass_different_name ok
test_SingularityFunction ok
test_deltas ok
test_hyper ok
test_meijerg ok
test_noncommutative ok
test_pretty_special_functions ok
test_pretty_geometry E
test_expint ok
test_elliptic_functions ok
test_RandomDomain ok
test_PrettyPoly ok
test_issue_6285 ok
test_issue_6359 ok
test_issue_6739 ok
test_complicated_symbol_unchanged ok
test_categories ok
test_PrettyModules ok
test_QuotientRing ok
test_Homomorphism ok
test_Tr ok
test_pretty_Add ok
test_issue_7179 ok
test_issue_7180 ok
test_pretty_Complement ok
test_pretty_SymmetricDifference ok
test_pretty_Contains ok
test_issue_8292 E
test_issue_4335 ok
test_issue_8344 E
test_issue_6324 ok
test_issue_7927 ok
test_issue_6134 ok
test_issue_9877 ok
test_issue_13651 ok
test_pretty_primenu ok
test_pretty_primeomega ok
test_pretty_Mod ok
test_issue_11801 ok
test_pretty_UnevaluatedExpr ok
test_issue_10472 ok
test_MatrixElement_printing ok
test_issue_12675 ok
test_MatrixSymbol_printing F
test_degree_printing ok
test_vector_expr_pretty_printing ok                                       [FAIL]


________________________________ xpassed tests _________________________________
sympy/printing/pretty/tests/test_pretty.py: test_missing_in_2X_issue_9047

________________________________________________________________________________
_______ sympy/printing/pretty/tests/test_pretty.py:test_pretty_geometry ________
  File "/testbed/sympy/printing/pretty/tests/test_pretty.py", line 5285, in test_pretty_geometry
    e = Ray((1, 1), angle=4.02*pi)
  File "/testbed/sympy/geometry/line.py", line 1275, in __new__
    return Ray2D(p1, p2, **kwargs)
  File "/testbed/sympy/geometry/line.py", line 2108, in __new__
    p2 = p1 + Point(x, y)
  File "/testbed/sympy/geometry/point.py", line 224, in __add__
    coords = [simplify(a + b) for a, b in zip(s, o)]
  File "/testbed/sympy/geometry/point.py", line 224, in <listcomp>
    coords = [simplify(a + b) for a, b in zip(s, o)]
  File "/testbed/sympy/simplify/simplify.py", line 613, in simplify
    short = exptrigsimp(short)
  File "/testbed/sympy/simplify/trigsimp.py", line 542, in exptrigsimp
    newexpr = bottom_up(expr, exp_trig)
  File "/testbed/sympy/simplify/simplify.py", line 1086, in bottom_up
    args = tuple([bottom_up(a, F, atoms, nonbasic)
  File "/testbed/sympy/simplify/simplify.py", line 1086, in <listcomp>
    args = tuple([bottom_up(a, F, atoms, nonbasic)
  File "/testbed/sympy/simplify/simplify.py", line 1090, in bottom_up
    rv = F(rv)
  File "/testbed/sympy/simplify/trigsimp.py", line 539, in exp_trig
    choices.append(e.rewrite(exp))
  File "/testbed/sympy/core/basic.py", line 1670, in rewrite
    return self._eval_rewrite(None, rule, **hints)
  File "/testbed/sympy/core/basic.py", line 1578, in _eval_rewrite
    rewritten = getattr(self, rule)(*args)
  File "/testbed/sympy/functions/elementary/trigonometric.py", line 1147, in _eval_rewrite_as_exp
    neg_exp, pos_exp = exp(-arg*I), exp(arg*I)
  File "/testbed/sympy/core/function.py", line 441, in __new__
    result = super(Function, cls).__new__(cls, *args, **options)
  File "/testbed/sympy/core/function.py", line 250, in __new__
    evaluated = cls.eval(*args)
  File "/testbed/sympy/functions/elementary/exponential.py", line 257, in eval
    if ask(Q.integer(2*coeff)):
  File "/testbed/sympy/assumptions/ask.py", line 1247, in ask
    from sympy.assumptions.satask import satask
  File "/testbed/sympy/assumptions/satask.py", line 9, in <module>
    from sympy.assumptions.sathandlers import fact_registry
  File "/testbed/sympy/assumptions/sathandlers.py", line 3, in <module>
    from collections import MutableMapping, defaultdict
  File "<frozen importlib._bootstrap>", line 1055, in _handle_fromlist
  File "/opt/miniconda3/envs/testbed/lib/python3.9/collections/__init__.py", line 62, in __getattr__
    warnings.warn("Using or importing the ABCs from 'collections' instead "
DeprecationWarning: Using or importing the ABCs from 'collections' instead of from 'collections.abc' is deprecated since Python 3.3, and in 3.10 it will stop working
________________________________________________________________________________
__________ sympy/printing/pretty/tests/test_pretty.py:test_issue_8292 __________
  File "/testbed/sympy/printing/pretty/tests/test_pretty.py", line 5835, in test_issue_8292
    e = sympify('((x+x**4)/(x-1))-(2*(x-1)**4/(x-1)**4)', evaluate=False)
  File "/testbed/sympy/core/sympify.py", line 368, in sympify
    expr = parse_expr(a, local_dict=locals, transformations=transformations, evaluate=evaluate)
  File "/testbed/sympy/parsing/sympy_parser.py", line 948, in parse_expr
    code = compile(evaluateFalse(code), '<string>', 'eval')
ValueError: Name node can't be used with 'False' constant
________________________________________________________________________________
__________ sympy/printing/pretty/tests/test_pretty.py:test_issue_8344 __________
  File "/testbed/sympy/printing/pretty/tests/test_pretty.py", line 5876, in test_issue_8344
    e = sympify('2*x*y**2/1**2 + 1', evaluate=False)
  File "/testbed/sympy/core/sympify.py", line 368, in sympify
    expr = parse_expr(a, local_dict=locals, transformations=transformations, evaluate=evaluate)
  File "/testbed/sympy/parsing/sympy_parser.py", line 948, in parse_expr
    code = compile(evaluateFalse(code), '<string>', 'eval')
ValueError: Name node can't be used with 'False' constant

________________________________________________________________________________
____ sympy/printing/pretty/tests/test_pretty.py:test_MatrixSymbol_printing _____
  File "/testbed/sympy/printing/pretty/tests/test_pretty.py", line 6122, in test_MatrixSymbol_printing
    assert pretty(A*B*C - A*B - B*C) == "-A*B -B*C + A*B*C"
AssertionError

 tests finished: 112 passed, 1 failed, 1 expected to fail but passed,
3 exceptions, in 13.91 seconds
DO *NOT* COMMIT!


