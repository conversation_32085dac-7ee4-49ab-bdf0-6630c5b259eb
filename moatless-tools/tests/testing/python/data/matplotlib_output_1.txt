============================= test session starts ==============================
platform linux -- Python 3.11.9, pytest-8.2.2, pluggy-1.5.0
rootdir: /testbed
configfile: pytest.ini
plugins: rerunfailures-14.0, timeout-2.3.1, cov-5.0.0, xdist-3.6.1
collected 48 items

lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py ............FF..... [ 39%]
.............................                                            [100%]

=================================== FAILURES ===================================
________________ test_image_grid_each_left_label_mode_all[png] _________________

args = ()
kwds = {'extension': 'png', 'request': <FixtureRequest for <Function test_image_grid_each_left_label_mode_all[png]>>}

    @wraps(func)
    def inner(*args, **kwds):
        with self._recreate_cm():
>           return func(*args, **kwds)

/opt/miniconda3/envs/testbed/lib/python3.11/contextlib.py:81:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
lib/matplotlib/testing/compare.py:466: in compare_images
    rms = calculate_rms(expected_image, actual_image)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

expected_image = array([[[255, 255, 255],
        [255, 255, 255],
        [255, 255, 255],
        ...,
        [255, 255, 255],
     ...[255, 255, 255],
        ...,
        [255, 255, 255],
        [255, 255, 255],
        [255, 255, 255]]], dtype=int16)
actual_image = array([[[255, 255, 255],
        [255, 255, 255],
        [255, 255, 255],
        ...,
        [255, 255, 255],
     ...[255, 255, 255],
        ...,
        [255, 255, 255],
        [255, 255, 255],
        [255, 255, 255]]], dtype=int16)

    def calculate_rms(expected_image, actual_image):
        """
        Calculate the per-pixel errors, then compute the root mean square error.
        """
        if expected_image.shape != actual_image.shape:
>           raise ImageComparisonFailure(
                f"Image sizes do not match expected size: {expected_image.shape} "
                f"actual size {actual_image.shape}")
E           matplotlib.testing.exceptions.ImageComparisonFailure: Image sizes do not match expected size: (214, 279, 3) actual size (195, 279, 3)

lib/matplotlib/testing/compare.py:363: ImageComparisonFailure
______________________ test_image_grid_single_bottom[png] ______________________

args = ()
kwds = {'extension': 'png', 'request': <FixtureRequest for <Function test_image_grid_single_bottom[png]>>}

    @wraps(func)
    def inner(*args, **kwds):
        with self._recreate_cm():
>           return func(*args, **kwds)

/opt/miniconda3/envs/testbed/lib/python3.11/contextlib.py:81:
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _
lib/matplotlib/testing/compare.py:466: in compare_images
    rms = calculate_rms(expected_image, actual_image)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _

expected_image = array([[[255, 255, 255],
        [255, 255, 255],
        [255, 255, 255],
        ...,
        [255, 255, 255],
     ...[255, 255, 255],
        ...,
        [255, 255, 255],
        [255, 255, 255],
        [255, 255, 255]]], dtype=int16)
actual_image = array([[[255, 255, 255],
        [255, 255, 255],
        [255, 255, 255],
        ...,
        [255, 255, 255],
     ...[255, 255, 255],
        ...,
        [255, 255, 255],
        [255, 255, 255],
        [255, 255, 255]]], dtype=int16)

    def calculate_rms(expected_image, actual_image):
        """
        Calculate the per-pixel errors, then compute the root mean square error.
        """
        if expected_image.shape != actual_image.shape:
>           raise ImageComparisonFailure(
                f"Image sizes do not match expected size: {expected_image.shape} "
                f"actual size {actual_image.shape}")
E           matplotlib.testing.exceptions.ImageComparisonFailure: Image sizes do not match expected size: (166, 288, 3) actual size (166, 274, 3)

lib/matplotlib/testing/compare.py:363: ImageComparisonFailure
==================================== PASSES ====================================
=========================== short test summary info ============================
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_divider_append_axes
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_twin_axes_empty_and_removed[png]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_axesgrid_colorbar_log_smoketest
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_inset_colorbar_tight_layout_smoketest
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_inset_locator[png]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_inset_axes[png]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_inset_axes_complete
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_fill_facecolor[png]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_zooming_with_inverted_axes[png]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_anchored_direction_arrows[png]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_anchored_direction_arrows_many_args[png]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_axes_locatable_position
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_image_grid_label_mode_deprecation_warning
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_image_grid[png]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_gettightbbox
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_picking_callbacks_overlap[gca-gca-big]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_picking_callbacks_overlap[gca-gca-small]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_picking_callbacks_overlap[host-host-big]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_picking_callbacks_overlap[host-host-small]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_picking_callbacks_overlap[host-parasite-big]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_picking_callbacks_overlap[host-parasite-small]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_picking_callbacks_overlap[parasite-host-big]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_picking_callbacks_overlap[parasite-host-small]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_picking_callbacks_overlap[parasite-parasite-big]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_picking_callbacks_overlap[parasite-parasite-small]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_anchored_artists[png]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_hbox_divider
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_vbox_divider
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_axes_class_tuple
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_grid_axes_lists
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_grid_axes_position[row]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_grid_axes_position[column]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_grid_errors[rect0-None-TypeError-Incorrect rect format]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_grid_errors[111--1-ValueError-ngrids must be positive]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_grid_errors[111-7-ValueError-ngrids must be positive]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_divider_errors[None-TypeError-anchor must be str]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_divider_errors[CC-ValueError-'CC' is not a valid value for anchor]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_divider_errors[anchor2-TypeError-anchor must be str]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_mark_inset_unstales_viewlim[png]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_auto_adjustable
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_rgb_axes[png]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_insetposition[png]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_imagegrid_cbar_mode_edge[png]
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_imagegrid
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_removal
PASSED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_anchored_locator_base_call[png]
FAILED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_image_grid_each_left_label_mode_all[png]
FAILED lib/mpl_toolkits/axes_grid1/tests/test_axes_grid1.py::test_image_grid_single_bottom[png]
======================== 2 failed, 46 passed in 22.28s =========================
