Updated 0 paths from cb08383bf7
Checking patch tests/auth_tests/test_validators.py...
Applied patch tests/auth_tests/test_validators.py cleanly.
>>>>> Run tests
Creating test database for alias 'default' ('file:memorydb_default?mode=memory&cache=shared')...
test_help_text (auth_tests.test_validators.UserAttributeSimilarityValidatorTest.test_help_text) ... ok
test_validate (auth_tests.test_validators.UserAttributeSimilarityValidatorTest.test_validate) ... ok
test_validate_property (auth_tests.test_validators.UserAttributeSimilarityValidatorTest.test_validate_property) ... ok
test_help_text (auth_tests.test_validators.CommonPasswordValidatorTest.test_help_text) ... ok
test_validate (auth_tests.test_validators.CommonPasswordValidatorTest.test_validate) ... ok
test_validate_custom_list (auth_tests.test_validators.CommonPasswordValidatorTest.test_validate_custom_list) ... ok
test_validate_django_supplied_file (auth_tests.test_validators.CommonPasswordValidatorTest.test_validate_django_supplied_file) ... ok
test_help_text (auth_tests.test_validators.MinimumLengthValidatorTest.test_help_text) ... ok
test_validate (auth_tests.test_validators.MinimumLengthValidatorTest.test_validate) ... ok
test_help_text (auth_tests.test_validators.NumericPasswordValidatorTest.test_help_text) ... ok
test_validate (auth_tests.test_validators.NumericPasswordValidatorTest.test_validate) ... ok
test_empty_password_validator_help_text_html (auth_tests.test_validators.PasswordValidationTest.test_empty_password_validator_help_text_html) ... ok
test_get_default_password_validators (auth_tests.test_validators.PasswordValidationTest.test_get_default_password_validators) ... ok
test_get_password_validators_custom (auth_tests.test_validators.PasswordValidationTest.test_get_password_validators_custom) ... ok
test_password_changed (auth_tests.test_validators.PasswordValidationTest.test_password_changed) ... ok
test_password_changed_with_custom_validator (auth_tests.test_validators.PasswordValidationTest.test_password_changed_with_custom_validator) ... ok
test_password_validators_help_text_html (auth_tests.test_validators.PasswordValidationTest.test_password_validators_help_text_html) ... ok
test_password_validators_help_text_html_escaping (auth_tests.test_validators.PasswordValidationTest.test_password_validators_help_text_html_escaping) ... ok
test_password_validators_help_texts (auth_tests.test_validators.PasswordValidationTest.test_password_validators_help_texts) ... ok
test_validate_password (auth_tests.test_validators.PasswordValidationTest.test_validate_password) ... ok
test_ascii_validator (auth_tests.test_validators.UsernameValidatorsTests.test_ascii_validator) ... ok
test_unicode_validator (auth_tests.test_validators.UsernameValidatorsTests.test_unicode_validator) ... ok

----------------------------------------------------------------------
Ran 22 tests in 0.030s

OK
Destroying test database for alias 'default' ('file:memorydb_default?mode=memory&cache=shared')...
Testing against Django installed in '/testbed/django'
Importing application auth_tests
Skipping setup of unused database(s): other.
Operations to perform:
  Synchronize unmigrated apps: auth, auth_tests, contenttypes, messages, sessions, staticfiles
  Apply all migrations: admin, sites
Synchronizing apps without migrations:
  Creating tables...
    Creating table django_content_type
    Creating table auth_permission
    Creating table auth_group
    Creating table auth_user
    Creating table django_session
    Creating table auth_tests_customuser
    Creating table auth_tests_customuserwithoutisactivefield
    Creating table auth_tests_extensionuser
    Creating table auth_tests_custompermissionsuser
    Creating table auth_tests_customusernonuniqueusername
    Creating table auth_tests_isactivetestuser1
    Creating table auth_tests_minimaluser
    Creating table auth_tests_nopassworduser
    Creating table auth_tests_concrete
    Creating table auth_tests_uuiduser
    Creating table auth_tests_email
    Creating table auth_tests_customuserwithfk
    Creating table auth_tests_integerusernameuser
    Creating table auth_tests_userwithdisabledlastloginfield
    Running deferred SQL...
Running migrations:
  Applying admin.0001_initial... OK
  Applying admin.0002_logentry_remove_auto_add... OK
  Applying admin.0003_logentry_add_action_flag_choices... OK
  Applying sites.0001_initial... OK
  Applying sites.0002_alter_domain_unique... OK
System check identified no issues (0 silenced).
Updated 1 path from cb08383bf7

