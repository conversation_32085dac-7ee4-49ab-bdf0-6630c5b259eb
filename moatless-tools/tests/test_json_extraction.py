from moatless.completion.schema import extract_json_from_message


def test_extract_single_json_codeblock():
    message = """Some text before
    ```json
    {"key": "value"}
    ```
    Some text after"""
    json_obj, all_jsons = extract_json_from_message(message)
    assert json_obj == {"key": "value"}
    assert len(all_jsons) == 1
    assert all_jsons[0] == {"key": "value"}


def test_extract_multiple_json_codeblocks():
    message = """
    ```json
    {"first": "block"}
    ```
    Some text
    ```json
    {"second": "block"}
    ```"""
    json_obj, all_jsons = extract_json_from_message(message)
    assert json_obj == {"first": "block"}
    assert len(all_jsons) == 2
    assert {"second": "block"} in all_jsons


def test_extract_raw_json():
    message = 'Some text {"key": "value"} more text'
    json_obj, all_jsons = extract_json_from_message(message)
    assert json_obj == {"key": "value"}
    assert len(all_jsons) == 1


def test_extract_multiple_raw_jsons():
    message = 'First {"key1": "value1"} second {"key2": "value2"}'
    json_obj, all_jsons = extract_json_from_message(message)
    assert json_obj == {"key1": "value1"}
    assert len(all_jsons) == 2
    assert {"key2": "value2"} in all_jsons


def test_no_json_found():
    message = "Just some plain text without JSON"
    result, all_jsons = extract_json_from_message(message)
    assert result == message
    assert len(all_jsons) == 0


def test_invalid_json():
    message = """
    ```json
    {"invalid": "json"
    ```"""
    result, all_jsons = extract_json_from_message(message)
    assert result == message
    assert len(all_jsons) == 0


def test_nested_json():
    message = """
    ```json
    {
        "outer": {
            "inner": {"nested": "value"}
        }
    }
    ```"""
    json_obj, all_jsons = extract_json_from_message(message)
    assert json_obj == {"outer": {"inner": {"nested": "value"}}}
    assert len(all_jsons) == 1


def test_real_world_example():
    message = """<thoughts>
The previous AI agent's intention was to find the specific code spans in Django's admin that use the `reverse` function to generate URLs for model instances. The <search_results> provided include parts of `django/contrib/admin/options.py` and `tests/admin_views/admin.py`. The relevant parts should be focused on functions or methods that use `reverse` to generate URLs.

In `django/contrib/admin/options.py`, the `reverse` function is imported from `django.urls`. The relevant code spans should include methods that utilize this `reverse` function.

From the provided results, the `get_urls` method in the `ModelAdmin` class is a potential candidate as it likely involves URL generation. However, a more thorough examination is needed to identify other possible uses of `reverse` within the context of URL generation for model instances.
</thoughts>

<search_parameters>
{"file_pattern":null,"query":"code that uses reverse to generate URLs for model instances in Django admin","category":"implementation"}
</search_parameters>

<search_results>
django/contrib/admin/options.py
```python
<span id='ModelAdmin.get_urls'>

    def get_urls(self):
        from django.urls import path

        def wrap(view):
            def wrapper(*args, **kwargs):
                return self.admin_site.admin_view(view)(*args, **kwargs)
            wrapper.model_admin = self
            return update_wrapper(wrapper, view)

        info = self.model._meta.app_label, self.model._meta.model_name

        urlpatterns = [
            path('', wrap(self.changelist_view), name='%s_%s_changelist' % info),
            path('<path:object_id>/', wrap(self.change_view), name='%s_%s_change' % info),
            path('<path:object_id>/history/', wrap(self.history_view), name='%s_%s_history' % info),
            path('<path:object_id>/delete/', wrap(self.delete_view), name='%s_%s_delete' % info),
            path('add/', wrap(self.add_view), name='%s_%s_add' % info),
        ]
        return urlpatterns
```

The `get_urls` method above uses `path` to generate URL patterns, which internally uses `reverse` to resolve URLs. However, a more specific usage of `reverse` can be found in other parts of the file.
</search_results>

```python
<span id='ModelAdmin.response_add'>
    def response_add(self, request, obj, post_url_continue=None):
        \"""
        Determine the HttpResponse for the add_view stage.
        \"""
        opts = self.model._meta
        pk_value = obj._get_pk_val()
        preserved_filters = self.get_preserved_filters(request)

        msg_dict = {
            "name": force_str(opts.verbose_name),
            "obj": force_str(obj),
        }
        if "_addanother" in request.POST:
            msg = format_html(_("The {name} “{obj}” was added successfully. You may add another {name} below."),
                             **msg_dict)
            self.message_user(request, msg, messages.SUCCESS)
            redirect_url = request.path
            redirect_url = add_preserved_filters({'preserved_filters': preserved_filters, 'opts': opts}, redirect_url)
            return HttpResponseRedirect(redirect_url)

        elif "_continue" in request.POST:
            msg = format_html(
                _("The {name} “{obj}” was added successfully. You may edit it again below."),
                **msg_dict
            )
            self.message_user(request, msg, messages.SUCCESS)
            redirect_url = reverse(
                "%s:%s_%s_change" % (self.admin_site.name, opts.app_label, opts.model_name),
                args=(quote(pk_value),),
                current_app=self.admin_site.name,
            )
            redirect_url = add_preserved_filters({'preserved_filters': preserved_filters, 'opts': opts}, redirect_url)
            return HttpResponseRedirect(redirect_url)

        else:
            msg = format_html(_("The {name} “{obj}” was added successfully."), **msg_dict)
            self.message_user(request, msg, messages.SUCCESS)
            return self.response_post_save_add(request, obj)
```

```python
<span id='ModelAdmin.response_change'>
    def response_change(self, request, obj):
        \"""
        Determine the HttpResponse for the change_view stage.
        \"""
        opts = self.model._meta
        preserved_filters = self.get_preserved_filters(request)

        msg_dict = {"name": force_str(opts.verbose_name), "obj": force_str(obj)}
        if "_continue" in request.POST:
            msg = format_html(
                _("The {name} “{obj}” was changed successfully. You may edit it again below."),
                **msg_dict
            )
            self.message_user(request, msg, messages.SUCCESS)
            redirect_url = reverse(
                "%s:%s_%s_change" % (self.admin_site.name, opts.app_label, opts.model_name),
                args=(quote(obj.pk),),
                current_app=self.admin_site.name,
            )
            redirect_url = add_preserved_filters({'preserved_filters': preserved_filters, 'opts': opts}, redirect_url)
            return HttpResponseRedirect(redirect_url)

        elif "_saveasnew" in request.POST:
            msg = format_html(
                _("The {name} “{obj}” was added successfully. You may edit it again below."),
                **msg_dict
            )
            self.message_user(request, msg, messages.SUCCESS)
            redirect_url = reverse(
                "%s:%s_%s_change" % (self.admin_site.name, opts.app_label, opts.model_name),
                args=(quote(obj.pk),),
                current_app=self.admin_site.name,
            )
            redirect_url = add_preserved_filters({'preserved_filters': preserved_filters, 'opts': opts}, redirect_url)
            return HttpResponseRedirect(redirect_url)

        else:
            msg = format_html(_("The {name} “{obj}” was changed successfully."), **msg_dict)
            self.message_user(request, msg, messages.SUCCESS)
            return self.response_post_save_change(request, obj)
```

```python
<span id='ModelAdmin.response_delete'>
    def response_delete(self, request, obj_display, obj_id):
        \"""
        Determine the HttpResponse for the delete_view stage.
        \"""
        opts = self.model._meta
        preserved_filters = self.get_preserved_filters(request)

        msg_dict = {"name": force_str(opts.verbose_name), "obj": obj_display}
        msg = format_html(
            _("The {name} “{obj}” was deleted successfully."), **msg_dict
        )
        self.message_user(request, msg, messages.SUCCESS)

        if "_popup" in request.POST:
            return TemplateResponse(request, "admin/popup_response.html", {})

        redirect_url = reverse(
            "%s:%s_%s_changelist" % (self.admin_site.name, opts.app_label, opts.model_name),
            current_app=self.admin_site.name,
        )
        redirect_url = add_preserved_filters({'preserved_filters': preserved_filters, 'opts': opts}, redirect_url)
        return HttpResponseRedirect(redirect_url)
```

Based on the search results, the relevant code spans that use `reverse` to generate URLs for model instances in Django admin are found in the `response_add`, `response_change`, and `response_delete` methods within the `ModelAdmin` class.

```json
{
  "scratch_pad": "The relevant code spans that use 'reverse' to generate URLs for model instances in Django admin are found in the 'response_add', 'response_change', and 'response_delete' methods within the 'ModelAdmin' class.",
  "identified_spans": [
    {
      "file_path": "django/contrib/admin/options.py",
      "span_ids": ["ModelAdmin.response_add", "ModelAdmin.response_change", "ModelAdmin.response_delete"]
    }
  ]
}
```"""
    json_obj, all_jsons = extract_json_from_message(message)
    assert json_obj == {
        "scratch_pad": "The relevant code spans that use 'reverse' to generate URLs for model instances in Django admin are found in the 'response_add', 'response_change', and 'response_delete' methods within the 'ModelAdmin' class.",
        "identified_spans": [
            {
                "file_path": "django/contrib/admin/options.py",
                "span_ids": ["ModelAdmin.response_add", "ModelAdmin.response_change", "ModelAdmin.response_delete"],
            }
        ],
    }
    assert len(all_jsons) == 1
    assert all_jsons[0] == json_obj


def test_json_with_comments():
    message = """{
        "key1": "value1",  // This is a comment
        "key2": {
            "nested": "value2"  # Another comment
        }
    }"""
    json_obj, all_jsons = extract_json_from_message(message)
    assert json_obj == {"key1": "value1", "key2": {"nested": "value2"}}
    assert len(all_jsons) == 1


def test_real_world_example_with_comments():
    message = '```json\n{\n  "action": {\n    "scratch_pad": "Instead of overriding the _get_FIELD_display method, we should use the existing get_FOO_display method provided by Django for fields with choices. If custom display logic is needed, we extend this method in our model. We will add this custom logic to the existing model file or create a new model file if it does not exist.",\n    "path": "your_app/models.py",\n    "file_text": "from django.db import models\\nfrom django.utils.translation import gettext_lazy as _\\n\\nclass FooBar(models.Model):\\n    foo_bar = models.CharField(_("foo"), choices=[(1, \'foo\'), (2, \'bar\')])\\n\\n    def __str__(self):\\n        return self.get_foo_bar_display()\\n\\n    def get_foo_bar_display(self):\\n        value = super().get_foo_bar_display()\\n        if value == \'foo\':\\n            return \\"something\\"\\n        return value"\n  },\n  "action_type": "CreateFile"\n}\n```'
    json_obj, all_jsons = extract_json_from_message(message)
    print(json_obj)
