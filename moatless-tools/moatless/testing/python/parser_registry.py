import logging
from typing import Dict, List, Optional, Type

from moatless.testing.python.django_parser import DjangoParser
from moatless.testing.python.pytest_parser import PyTestParser
from moatless.testing.python.seaborn_parser import SeabornParser
from moatless.testing.python.sympy_parser import SympyParser
from moatless.testing.schema import TestResult, TestStatus
from moatless.testing.test_output_parser import TestOutputParser

logger = logging.getLogger(__name__)

# Map repository names to their respective parser classes
REPO_TO_PARSER_CLASS: Dict[str, Type[TestOutputParser]] = {
    "astropy/astropy": PyTestParser,
    "django/django": DjangoParser,
    "marshmallow-code/marshmallow": PyTestParser,
    "matplotlib/matplotlib": PyTestParser,
    "mwaskom/seaborn": SeabornParser,
    "pallets/flask": PyTest<PERSON>arser,
    "psf/requests": PyTestParser,
    "pvlib/pvlib-python": PyTestParser,
    "pydata/xarray": Py<PERSON>estPars<PERSON>,
    "pydicom/pydicom": PyTestParser,
    "pylint-dev/astroid": PyTestParser,
    "pylint-dev/pylint": PyTestParser,
    "pytest-dev/pytest": PyTestParser,
    "pyvista/pyvista": PyTestParser,
    "scikit-learn/scikit-learn": PyTestParser,
    "sqlfluff/sqlfluff": PyTestParser,
    "sphinx-doc/sphinx": PyTestParser,
    "sympy/sympy": SympyParser,
}


def get_parser_for_repo(repo: str) -> TestOutputParser:
    """
    Get the appropriate parser for a repository.

    Args:
        repo: Repository name

    Returns:
        TestOutputParser: A parser instance for the repository
    """
    parser_class = REPO_TO_PARSER_CLASS.get(repo, PyTestParser)
    return parser_class()


def parse_log(log: str, repo: str, file_path: Optional[str] = None) -> List[TestResult]:
    """
    Parse test log using the appropriate parser for the repository.

    Args:
        log: Test output log content
        repo: Repository name
        file_path: Optional file path to filter results for

    Returns:
        List[TestResult]: List of parsed test results
    """
    parser = get_parser_for_repo(repo)
    logger.info(f"Parsing log for {repo} with {parser.__class__.__name__}")
    test_results = parser.parse_test_output(log, file_path)
    logger.info(f"Parsed {len(test_results)} test results")

    if not test_results:
        return [
            TestResult(
                file_path=file_path,
                failure_output=log,
                status=TestStatus.UNKNOWN,
                error_details=log,
            )
        ]

    # Skip testbed prefix in file paths
    for result in test_results:
        if result.file_path and result.file_path.startswith("/testbed/"):
            result.file_path = result.file_path[len("/testbed/") :]

        if result.failure_output:
            result.failure_output = result.failure_output.replace("/testbed/", "")

        if not result.file_path:
            result.file_path = file_path

    return test_results
