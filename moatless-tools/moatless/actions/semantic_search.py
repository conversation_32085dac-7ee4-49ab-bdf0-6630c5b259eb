from typing import ClassVar, Optional

from pydantic import ConfigDict, Field, model_validator

from moatless.actions.schema import ActionArguments
from moatless.actions.search_base import SearchBaseAction, SearchBaseArgs
from moatless.completion.schema import FewShotExample
from moatless.index.types import SearchCodeResponse


class SemanticSearchArgs(SearchBaseArgs):
    """Use this when you don't know exact names or code but want to find related functionality.

    Perfect for:
    - Finding functionality by description: query="code that handles password hashing"
    - Finding related test cases: query="tests for user registration", category="test"
    - Finding implementations: query="database connection pooling", category="implementation"
    - Finding patterns: query="error handling for API requests"

    This is the most flexible search when you:
    - Don't know exact function/class names
    - Want to explore how certain features are implemented
    """

    query: str = Field(..., description="Natural language description of what you're looking for.")
    category: Optional[str] = Field(
        "implementation",
        description="The category of files to search for. This can be 'implementation' for core implementation files or 'test' for test files.",
    )

    model_config = ConfigDict(title="SemanticSearch")

    def to_prompt(self):
        prompt = f"Searching for code using the query: {self.query}"
        if self.file_pattern:
            prompt += f" in files matching the pattern: {self.file_pattern}"
        return prompt

    @model_validator(mode="after")
    def validate_query(self) -> "SemanticSearchArgs":
        if not self.query.strip():
            raise ValueError("query cannot be empty")
        return self

    def short_summary(self) -> str:
        param_str = f"query={self.query[:20]}, category={self.category}"
        if self.file_pattern:
            param_str += f", file_pattern={self.file_pattern}"
        return f"{self.name}({param_str})"

    @classmethod
    def get_few_shot_examples(cls) -> list[FewShotExample]:
        return [
            FewShotExample.create(
                user_input="Find all implementations of database connection pooling in our codebase",
                action=SemanticSearchArgs(
                    thoughts="To find implementations of database connection pooling, we should search for code related to managing database connections efficiently. This might include classes or functions that handle connection creation, reuse, and management.",
                    query="database connection pooling implementation",
                    category="implementation",
                ),
            ),
            FewShotExample.create(
                user_input="We need to find all test cases related to user authentication in our test suite",
                action=SemanticSearchArgs(
                    thoughts="To find test cases related to user authentication, we should search for test files that contain assertions and scenarios specifically testing authentication functionality.",
                    query="user authentication test cases",
                    file_pattern="tests/*.py",
                    category="test",
                ),
            ),
        ]


class SemanticSearch(SearchBaseAction):
    args_schema: ClassVar[type[ActionArguments]] = SemanticSearchArgs

    async def _search(self, args: SemanticSearchArgs) -> SearchCodeResponse:
        return await self.code_index.semantic_search(
            args.query,
            file_pattern=args.file_pattern,
            max_results=self.max_hits,
            category=args.category,
        )

    async def _search_for_alternative_suggestion(self, args: SemanticSearchArgs) -> SearchCodeResponse:
        if args.file_pattern:
            return await self.code_index.semantic_search(
                args.query,
                max_results=self.max_hits,
                category=args.category,
            )

        return SearchCodeResponse()

    @classmethod
    def get_evaluation_criteria(cls, trajectory_length: int | None = None) -> list[str]:
        criteria = super().get_evaluation_criteria(trajectory_length)
        criteria.extend(
            [
                "Query Relevance: Evaluate if the search query is well-defined and likely to find relevant code.",
                "Category Appropriateness: Assess if the category (implementation or test) aligns with the search intent.",
            ]
        )
        return criteria
