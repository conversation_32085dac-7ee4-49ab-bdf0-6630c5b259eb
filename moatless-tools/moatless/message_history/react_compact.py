import logging
from typing import List

from moatless.actions.schema import ActionArguments
from moatless.actions.view_code import View<PERSON>ode<PERSON>rg<PERSON>, CodeSpan
from moatless.completion.schema import (
    AllMessageValues,
    ChatCompletionAssistantMessage,
    ChatCompletionUserMessage,
)
from moatless.message_history.compact import CompactMessageHistoryGenerator, NodeMessage
from moatless.node import Node, ActionStep
from moatless.utils.tokenizer import count_tokens
from moatless.workspace import Workspace
from pydantic import Field

logger = logging.getLogger(__name__)


class ReactCompactMessageHistoryGenerator(CompactMessageHistoryGenerator):
    async def generate_messages(self, node: Node, workspace: Workspace) -> list[AllMessageValues]:
        node_messages = await self.get_node_messages(node)
        logger.info(f"Node messages: {len(node_messages)}")

        messages = []

        # Convert node messages to react format
        for node_message in node_messages:
            if node_message.user_message:
                messages.append(ChatCompletionUserMessage(role="user", content=node_message.user_message))

            if node_message.assistant_message:
                messages.append(
                    ChatCompletionAssistantMessage(role="assistant", content=node_message.assistant_message)
                )

            if not node_message.actions:
                continue

            assistant_content = ""
            user_content = "Observations:\n"

            has_actions = False

            for action, observation in zip(node_message.actions, node_message.observations, strict=True):
                from moatless.actions.think import ThinkArgs

                if isinstance(action, ThinkArgs):
                    assistant_content += f"Thought: {action.thought}\n"
                    continue
                elif hasattr(action, "thoughts") and action.thoughts:
                    assistant_content += f"Thought: {action.thoughts}\n"
                elif isinstance(action, ViewCodeArgs) and action.files and len(action.files) > 0:
                    # Special handling for ViewCode actions to include file path in thought
                    file_path = action.files[0].file_path
                    assistant_content += f"Thought: Let's view the content in {file_path}\n"
                # Special case for TestActionArguments in test fixtures
                elif action.__class__.__name__ == "TestActionArguments" and hasattr(action, "__view_file__"):
                    file_path = action.__view_file__
                    assistant_content += f"Thought: Let's view the content in {file_path}\n"
                else:
                    assistant_content += "\n"

                has_actions = True

                assistant_content += f"Action: {action.name}\n"
                assistant_content += action.format_args_for_llm()

                if observation:
                    user_content += f"Observation: {observation}\n"

            if has_actions:
                messages.append(ChatCompletionAssistantMessage(role="assistant", content=assistant_content))
                messages.append(ChatCompletionUserMessage(role="user", content=user_content))

        tokens = count_tokens("".join([m.get("content", "") for m in messages if isinstance(m.get("content"), str)]))
        logger.info(f"Generated {len(messages)} messages with {tokens} tokens")
        return messages
