{"name": "swegym_mini", "description": "SWE-GYM Mini", "instance_ids": ["getmoto__moto-4990", "modin-project__modin-7208", "getmoto__moto-7012", "iterative__dvc-1681", "iterative__dvc-3797", "getmoto__moto-6226", "getmoto__moto-6410", "getmoto__moto-6022", "dask__dask-10695", "python__mypy-15208", "Project-MONAI__MONAI-3690", "pydantic__pydantic-9066", "dask__dask-8792", "pydantic__pydantic-6104", "facebookresearch__hydra-2189", "dask__dask-8185", "dask__dask-8686", "getmoto__moto-6299", "getmoto__moto-6355", "getmoto__moto-5286", "dask__dask-6626", "iterative__dvc-5161", "conan-io__conan-11799", "getmoto__moto-5347", "getmoto__moto-5109", "python__mypy-10284", "getmoto__moto-5980", "conan-io__conan-10875", "dask__dask-9235", "getmoto__moto-7061", "pydantic__pydantic-8793", "pydantic__pydantic-8500", "iterative__dvc-5004", "getmoto__moto-5072", "facebookresearch__hydra-1661", "Project-MONAI__MONAI-3566", "pandas-dev__pandas-48106", "iterative__dvc-9395", "getmoto__moto-6854", "getmoto__moto-7635", "getmoto__moto-5752", "modin-project__modin-6370", "bokeh__bokeh-12841", "iterative__dvc-4719", "dask__dask-9213", "Project-MONAI__MONAI-3715", "Project-MONAI__MONAI-4688", "conan-io__conan-13230", "getmoto__moto-7111", "getmoto__moto-5406", "getmoto__moto-6041", "getmoto__moto-6470", "getmoto__moto-6828", "dask__dask-10521", "conan-io__conan-13610", "conan-io__conan-15931", "python__mypy-10382", "dask__dask-9027", "getmoto__moto-4860", "Project-MONAI__MONAI-4925", "python__mypy-12741", "iterative__dvc-5148", "Project-MONAI__MONAI-1121", "bokeh__bokeh-13422", "getmoto__moto-6913", "conan-io__conan-14164", "getmoto__moto-5699", "getmoto__moto-7495", "getmoto__moto-5865", "getmoto__moto-5321", "getmoto__moto-6408", "iterative__dvc-1980", "iterative__dvc-6284", "facebookresearch__hydra-2543", "getmoto__moto-6906", "Project-MONAI__MONAI-4159", "iterative__dvc-4185", "pydantic__pydantic-8977", "conan-io__conan-13403", "bokeh__bokeh-13289", "iterative__dvc-3315", "getmoto__moto-5587", "getmoto__moto-7335", "iterative__dvc-6683", "dask__dask-6818", "python__mypy-16966", "pydantic__pydantic-9214", "getmoto__moto-5338", "python__mypy-11966", "python__mypy-11945", "getmoto__moto-4986", "bokeh__bokeh-13370", "getmoto__moto-5502", "iterative__dvc-4124", "dask__dask-8954", "Project-MONAI__MONAI-4775", "bokeh__bokeh-13318", "iterative__dvc-5839"]}