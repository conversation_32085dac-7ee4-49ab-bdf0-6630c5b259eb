[pytest]
log_cli=true
log_level=INFO
log_format = %(asctime)s - %(name)s - %(levelname)s - %(message)s
log_date_format = %Y-%m-%d %H:%M:%S
testpaths = tests
python_files = test_*.py
filterwarnings =
    ignore::pytest.PytestCollectionWarning
    ignore:builtin type SwigPyPacked has no __module__ attribute:DeprecationWarning
    ignore:builtin type SwigPyObject has no __module__ attribute:DeprecationWarning
    ignore:builtin type swigvarlink has no __module__ attribute:DeprecationWarning

