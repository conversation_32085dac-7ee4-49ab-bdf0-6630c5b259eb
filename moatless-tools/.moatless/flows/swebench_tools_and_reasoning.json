{"id": "swebench_tools_and_reasoning", "description": "SWE-bench flow using structured tool calling with Claude Sonnet 4 and built-in reasoning capabilities. ", "project_id": null, "trajectory_id": null, "metadata": {}, "max_iterations": 100, "max_cost": 1.5, "selector": {"selector_class": "moatless.selector.simple.SimpleSelector"}, "expander": {"max_expansions": 1, "auto_expand_root": false, "expander_class": "moatless.expander.expander.Expander"}, "value_function": {"value_function_class": "moatless.value_function.swebench.SwebenchValueFunction"}, "feedback_generator": null, "discriminator": null, "max_expansions": 1, "min_finished_nodes": 1, "max_finished_nodes": 1, "reward_threshold": 0.0, "max_depth": 100, "flow_class": "moatless.flow.search_tree.SearchTree", "agent": {"description": null, "completion_model": {"model": "claude-sonnet-4-20250514", "temperature": 1.0, "max_tokens": 16000, "timeout": 120.0, "model_base_url": null, "model_api_key": null, "metadata": null, "message_cache": true, "thoughts_in_action": false, "disable_thoughts": false, "few_shot_examples": false, "headers": {"anthropic-beta": "interleaved-thinking-2025-05-14"}, "params": {"thinking": {"type": "enabled", "budget_tokens": 10000}}, "merge_same_role_messages": false, "completion_model_class": "moatless.completion.tool_call.ToolCallCompletionModel"}, "system_prompt": "You are an autonomous AI assistant with superior programming skills. As you're working autonomously, you cannot communicate with the user but must rely on information you can get from the available functions.\n\n# Workflow Overview\n\n1. Understand the Issue\n- **Read the user’s initial message** in full; that is where the issue is described.\n\n2. **Locate Code**\n- Determine exactly which classes, functions, files, or components must change.\n- Identify any related code, dependencies, config, fixtures, or tests required to implement and verify the change.\n- Use `SemanticSearch`, `FindClass`, `FindFunction` and/or `FindCodeSnippet` to find the right code. \n- Use `GlobTool` and/or `ListFiles` to locate relevant files. \n- **Batch your search calls:** Group multiple search queries into one request when you know you’ll need them together.\n  - e.g. call tools all at once to retrieve a map of relevant file paths and code positions when possible. \n- *Extract file ranges:** Once you have file lists and line ranges, use one `ReadFile` call to fetch all necessary sections in bulk.\n\n3. **Modify Code**\n   * **Fix Task:** Make necessary code changes to resolve the task requirements.\n   * **Apply Changes:**\n     * StringReplace - Replace exact text strings in files with new content.\n     * CreateFile - Create new files with specified content.\n     * AppendString - Add content to the end of files.\n   * **Parallel Tool Calls:** When more than one tool call can be performed simultaneously or when tasks are closely related, combine these tool calls into one unified response whenever possible.\n\n4. **Verify Changes**\n   * **Find Tests:** Use the same search and read code tools as step 2 to find:\n     * Existing test files and test functions.\n     * Related test cases for modified components.\n     * Test utilities and helper functions.\n  * Always call `RunTests` with **all** relevant test file paths in one go.\n  *  Analyze failures and loop back: if tests fail, update tests and/or code as needed.\n\n\n5. **Modify Tests**\n   * **Update Tests:** Use the code modification tool calls from step 3 to:\n     * Update existing tests to match code changes.\n     * Add new test cases for added functionality.\n     * Test edge cases, error conditions, and boundary values.\n     * Verify error handling and invalid inputs.\n- **Do not** perform any cleanup of temporary files or test artifacts you created.\n\n6. **Iterate as Needed**\n   * Continue the process until all changes are complete and verified with new tests.\n\n7. **Complete Task**\n- When complete, call `Finish` once, and include a brief summary of:\n  - What was changed.\n  - Which tests were updated or added.\n  - Confirmation that tests now pass.\n\n# Important Guidelines\n\n- **Batching & Parallelism:**  \n  Always group related searches, reads, modifications, and test runs into as few function calls as possible. Don’t fetch or edit one file at a time if you know you need several.\n\n- **Minimal Scope:**  \n  Only touch code directly relevant to the issue. No extra refactors or unrelated cleanups.\n\n- **State Awareness:**  \n  Keep track of what you’ve already fetched or modified—avoid redundant calls.\n\n- **Single-Pass Finish:**  \n  Only one final `Finish` call when everything is done; combine your summary of changes and test results there.\n\n", "actions": [{"is_terminal": false, "hidden": false, "action_class": "moatless.actions.append_string.AppendString"}, {"is_terminal": false, "hidden": false, "action_class": "moatless.actions.create_file.CreateFile"}, {"completion_model": null, "is_terminal": false, "hidden": false, "max_search_tokens": 2000, "max_identify_tokens": 8000, "max_identify_prompt_tokens": 16000, "max_hits": 10, "add_extra_context": false, "use_identifier": false, "action_class": "moatless.actions.find_class.FindClass"}, {"completion_model": null, "is_terminal": false, "hidden": false, "max_search_tokens": 2000, "max_identify_tokens": 8000, "max_identify_prompt_tokens": 16000, "max_hits": 10, "add_extra_context": false, "use_identifier": false, "action_class": "moatless.actions.find_code_snippet.FindCodeSnippet"}, {"completion_model": null, "is_terminal": false, "hidden": false, "max_search_tokens": 2000, "max_identify_tokens": 8000, "max_identify_prompt_tokens": 16000, "max_hits": 10, "add_extra_context": false, "use_identifier": false, "action_class": "moatless.actions.find_function.FindFunction"}, {"is_terminal": false, "hidden": false, "action_class": "moatless.actions.glob.GlobTool"}, {"is_terminal": false, "hidden": false, "ignored_dirs": [".git", ".cursor", ".mvn", ".venv"], "action_class": "moatless.actions.list_files.ListFiles"}, {"is_terminal": false, "hidden": false, "max_lines": 100, "action_class": "moatless.actions.read_file.ReadFile"}, {"is_terminal": true, "hidden": false, "action_class": "moatless.actions.reject.Reject"}, {"is_terminal": false, "hidden": false, "max_output_tokens": 4000, "action_class": "moatless.actions.run_tests.RunTests"}, {"completion_model": null, "is_terminal": false, "hidden": false, "max_search_tokens": 2000, "max_identify_tokens": 8000, "max_identify_prompt_tokens": 16000, "max_hits": 10, "add_extra_context": false, "use_identifier": false, "action_class": "moatless.actions.semantic_search.SemanticSearch"}, {"is_terminal": false, "hidden": false, "auto_correct_indentation": true, "action_class": "moatless.actions.string_replace.StringReplace"}, {"is_terminal": false, "hidden": false, "action_class": "moatless.actions.verified_finish.VerifiedFinish"}, {"is_terminal": false, "hidden": false, "action_class": "moatless.actions.bash.BashTool"}], "memory": {"max_tokens": 0, "max_tokens_per_observation": null, "include_file_context": true, "include_git_patch": true, "thoughts_in_action": false, "memory_class": "moatless.message_history.message_history.MessageHistoryGenerator"}, "agent_class": "moatless.agent.agent.ActionAgent"}}