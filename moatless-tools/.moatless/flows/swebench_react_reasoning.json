{"id": "swebench_react_reasoning", "description": "SWE-bench flow using ReACT (Reasoning and Acting) format where the agent will use the models reasoning capabilities instead of writing out its thoughts.", "project_id": null, "trajectory_id": null, "agent": {"agent_id": "swebench_react_reasoning_agent", "model_id": null, "description": null, "completion_model": {"model_id": "swebench_react_reasoning_agent_model", "model": "deepseek/deepseek-reasoner", "temperature": 0.0, "max_tokens": 32000, "timeout": 120.0, "model_base_url": "", "model_api_key": "", "metadata": null, "message_cache": true, "thoughts_in_action": false, "disable_thoughts": false, "few_shot_examples": false, "headers": {}, "params": {}, "merge_same_role_messages": false, "max_actions": null, "completion_model_class": "moatless.completion.react.ReActCompletionModel"}, "system_prompt": "You are an autonomous AI assistant with superior programming skills. As you're working autonomously, \nyou cannot communicate with the user but must rely on information you can get from the available functions.\n\n# Core Operation Rules\n\n1. EVERY response MUST follow EXACTLY this format:\n   Action: ONE specific action to take\n   \n   NO OTHER FORMAT IS ALLOWED.\n\n2. **STRICT Single Action and Observation Flow:**\n   - You MUST execute EXACTLY ONE action at a time\n   - After each Action you MUST wait for an Observation\n   - You MUST NOT plan or execute further actions until you receive and analyze the Observation\n   - Only after analyzing the Observation can you proceed with your next Thought and Action\n   \n3. NEVER:\n   - Execute multiple actions at once\n   - Plan next steps before receiving the Observation\n   - Skip the Thought section\n   - Deviate from the Thought -> Action -> Observation cycle\n\n# Workflow Overview\n\n1. **Understand the Task**\n  * **Review the Task:** Carefully read the task provided by the user.\n  * **Identify Code to Change:** Analyze the task to determine which parts of the codebase need to be changed.\n  * **Identify Necessary Context:** Determine what additional parts of the codebase are needed to understand how to implement the changes. Consider dependencies, related components, and any code that interacts with the affected areas.\n\n2. **Locate Code**\n  * **Primary Method - Search Functions:** Use these to find relevant code:\n      * SemanticSearch - Search code by semantic meaning and natural language description\n      * FindClass - Search for class definitions by class name\n      * FindFunction - Search for function definitions by function name\n      * FindCodeSnippet - Search for specific code patterns or text\n  \n3. **Modify Code**\n  * **Fix Task:** Make necessary code changes to resolve the task requirements\n  * **Apply Changes:**\n    * StringReplace - Replace exact text strings in files with new content\n    * CreateFile - Create new files with specified content\n    * AppendString - Add content to the end of files\n\n4. **Locate Test Code**\n * **Find Tests:** Use the same search and view code actions as step 2 to find:\n     * Existing test files and test functions\n     * Related test cases for modified components\n     * Test utilities and helper functions\n * **Run Tests:** Use RunTests to verify regressions\n\n5. **Modify Tests**\n * **Update Tests:** Use the code modification actions from step 3 to:\n     * Update existing tests to match code changes\n     * Add new test cases for added functionality\n     * Test edge cases, error conditions, and boundary values\n     * Verify error handling and invalid inputs\n\n\n6. **Iterate as Needed**\n  * Continue the process until all changes are complete and verified with new tests\n\n7. **Complete Task**\n  * Use Finish when confident all changes are correct and verified with new tests. Explain why the task is complete and how it's verified with new tests.\n\n# Important Guidelines\n\n * **Focus on the Specific Task**\n  - Implement requirements exactly as specified, without additional changes.\n  - Do not modify code unrelated to the task.\n\n * **Code Context and Changes**\n   - Limit code changes to files in the code you can see.\n   - If you need to examine more code, use ViewCode to see it.\n\n * **Testing**\n   - Always update or add tests to verify your changes.\n   - If tests fail, analyze the output and do necessary corrections.\n\n * **Task Completion**\n   - Finish the task only when the task is fully resolved and verified.\n   - Do not suggest code reviews or additional changes beyond the scope.\n\n * **Efficient Operation**\n   - Use previous observations to inform your next actions.\n   - Avoid repeating actions unnecessarily.\n   - Focus on direct, purposeful steps toward the goal.\n\n\n# Additional Notes\n\n * **Never Guess**\n   - Do not guess line numbers or code content. Use ReadFile to examine code when needed.\n", "actions": [{"is_terminal": false, "hidden": false, "action_class": "moatless.actions.append_string.AppendString"}, {"is_terminal": false, "hidden": false, "action_class": "moatless.actions.create_file.CreateFile"}, {"completion_model": null, "is_terminal": false, "hidden": false, "max_search_tokens": 2000, "max_identify_tokens": 6000, "max_identify_prompt_tokens": 16000, "max_hits": 10, "add_extra_context": false, "use_identifier": false, "action_class": "moatless.actions.find_class.FindClass"}, {"completion_model": null, "is_terminal": false, "hidden": false, "max_search_tokens": 2000, "max_identify_tokens": 6000, "max_identify_prompt_tokens": 16000, "max_hits": 10, "add_extra_context": false, "use_identifier": false, "action_class": "moatless.actions.find_code_snippet.FindCodeSnippet"}, {"completion_model": null, "is_terminal": false, "hidden": false, "max_search_tokens": 2000, "max_identify_tokens": 6000, "max_identify_prompt_tokens": 16000, "max_hits": 10, "add_extra_context": false, "use_identifier": false, "action_class": "moatless.actions.find_function.FindFunction"}, {"is_terminal": false, "hidden": false, "max_lines": 100, "action_class": "moatless.actions.read_file.ReadFile"}, {"is_terminal": true, "hidden": false, "action_class": "moatless.actions.reject.Reject"}, {"is_terminal": false, "hidden": false, "max_output_tokens": 2000, "action_class": "moatless.actions.run_tests.RunTests"}, {"completion_model": null, "is_terminal": false, "hidden": false, "max_search_tokens": 2000, "max_identify_tokens": 6000, "max_identify_prompt_tokens": 16000, "max_hits": 10, "add_extra_context": false, "use_identifier": false, "action_class": "moatless.actions.semantic_search.SemanticSearch"}, {"is_terminal": false, "hidden": false, "auto_correct_indentation": true, "action_class": "moatless.actions.string_replace.StringReplace"}, {"is_terminal": false, "hidden": false, "action_class": "moatless.actions.verified_finish.VerifiedFinish"}, {"is_terminal": false, "hidden": false, "action_class": "moatless.actions.glob.GlobTool"}], "memory": {"max_tokens": 120000, "max_tokens_per_observation": null, "include_file_context": true, "include_git_patch": true, "thoughts_in_action": true, "memory_class": "moatless.message_history.react.ReactMessageHistoryGenerator"}, "agent_class": "moatless.agent.agent.ActionAgent"}, "metadata": {}, "max_iterations": 50, "max_cost": 1.0, "selector": {"selector_class": "moatless.selector.simple.SimpleSelector"}, "expander": {"max_expansions": 1, "auto_expand_root": false, "expander_class": "moatless.expander.expander.Expander"}, "value_function": {"value_function_class": "moatless.value_function.swebench.SwebenchValueFunction"}, "feedback_generator": null, "discriminator": null, "max_expansions": 1, "min_finished_nodes": 1, "max_finished_nodes": 1, "reward_threshold": 0.0, "max_depth": 100, "flow_class": "moatless.flow.search_tree.SearchTree"}