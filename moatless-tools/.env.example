# Environment variables for Moatless Docker setup

# Storage locations - these are mounted volumes in docker-compose.yml
MOATLESS_DIR=./.moatless

# Source code locations
MOATLESS_SOURCE_DIR=
MOATLESS_RUNNER_MOUNT_SOURCE_DIR=
MOATLESS_COMPONENTS_PATH=

# Runner configuration
MOATLESS_RUNNER=docker
MOATLESS_MAX_TOTAL_JOBS=1 # Max number of jobs to run in parallell
MOATLESS_MAX_JOBS_PER_PROJECT=1 # Max number of jobs to run for one project

MOATLESS_STORAGE=file
MOATLESS_AUTH_ENABLED=false

# Redis configuration
REDIS_URL=redis://localhost:6379

# Logging
LITELLM_LOG=INFO
PYDANTIC_ERRORS_INCLUDE_URL=false

OPENAI_API_KEY=
ANTHROPIC_API_KEY=
VOYAGE_API_KEY=
OPENROUTER_API_KEY=
