version: '3.8'

networks:
  moatless-network:
    name: moatless-network
    driver: bridge

services:
  redis:
    image: redis:7-alpine
    networks:
      - moatless-network
    volumes:
      - redis-data:/data
    healthcheck:
      test: [ "C<PERSON>", "redis-cli", "ping" ]
      interval: 5s
      timeout: 5s
      retries: 5

  moatless-tools-api:
    image: aorwall/moatless-api:latest
    command: api
    networks:
      - moatless-network
    volumes:
      - ${MOATLESS_DIR}:/data/moatless
      - /var/run/docker.sock:/var/run/docker.sock
      - ${MOATLESS_SOURCE_DIR:-/dev/null}:/app/moatless/moatless
      - ${MOATLESS_COMPONENTS_PATH:-/dev/null}:/opt/components
      - ${CUSTOM_REQUIREMENTS_PATH:-./custom_requirements.txt}:/app/custom_requirements.txt
    environment:
      - REDIS_URL=redis://redis:6379
      - MOATLESS_DIR=/data/moatless
      - MOATLESS_COMPONENTS_PATH=/opt/components
      - WORKERS_COUNT=1
      - PYTHONPATH=/app
      - MOATLESS_MAX_TOTAL_JOBS=${MOATLESS_MAX_TOTAL_JOBS:-5}
      - MOATLESS_RUNNER=docker
      - MOATLESS_STORAGE=file
      - MOATLESS_HOST_DIR=${MOATLESS_DIR:-./data/moatless}
      - MOATLESS_HOST_RUNNER_SOURCE_DIR=${MOATLESS_SOURCE_DIR}
      - MOATLESS_HOST_COMPONENTS_PATH=${MOATLESS_COMPONENTS_PATH}
      - MOATLESS_DOCKER_NETWORK=moatless-network
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - VOYAGE_API_KEY=${VOYAGE_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - MOATLESS_AUTH_ENABLED=${MOATLESS_AUTH_ENABLED}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    depends_on:
      redis:
        condition: service_healthy

  moatless-ui:
    image: aorwall/moatless-ui:latest
    networks:
      - moatless-network
    ports:
      - "80:80"
    restart: unless-stopped
    environment:
      - VITE_API_BASE_URL=http://moatless-tools-api:8000/api
    depends_on:
      moatless-tools-api:
        condition: service_healthy

volumes:
  redis-data:
