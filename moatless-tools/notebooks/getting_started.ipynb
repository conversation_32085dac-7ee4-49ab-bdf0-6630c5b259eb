{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["This notebook demonstrates how to create a simple AI agent using the Moatless framework.\n", "\n", "- **CompletionModel**: Defines the language model being used, including its name, temperature, and API settings.\n", "- **Agent**: Configured with a system prompt that defines its personality and capabilities, similar to prompt engineering in other frameworks.\n", "- **Actions**: Functionalities available to the agent (equivalent to tool calls in LLM frameworks). These determine what the agent can do.\n", "\n", "In the example below, we create a minimal agent with only the `Respond` action, which allows it to reply to messages. We test it with `run_simple()`, which sends a message and expects a simple response back. "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello! How can I assist you today?\n"]}], "source": ["from moatless.actions import Respond\n", "from moatless.agent import ActionAgent\n", "from moatless.completion.tool_call import ToolCallCompletionModel\n", "\n", "completion_model = ToolCallCompletionModel(\n", "    model=\"gpt-4.1-mini\",\n", "    temperature=0.0,\n", "    model_api_key=\"\"\n", ")\n", "\n", "agent = ActionAgent(\n", "    completion_model=completion_model,\n", "    system_prompt=\"You are a helpful assistant that can answer questions.\",\n", "    actions=[\n", "        Respond()\n", "    ]\n", ")\n", "\n", "observation = await agent.run_simple(\"Hello\")\n", "\n", "print(observation.message)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 2}