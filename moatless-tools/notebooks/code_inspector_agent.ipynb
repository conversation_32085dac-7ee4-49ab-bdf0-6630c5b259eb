{"cells": [{"cell_type": "markdown", "id": "7fea6d23c616b470", "metadata": {}, "source": ["# Code Inspector Agent\n", "\n", "A simple example demonstrating how to build an agent that can search through codebases and answer questions about code structure. This agent uses custom actions to search code repositories and analyze findings, allowing developers to quickly understand unfamiliar codebases."]}, {"cell_type": "markdown", "id": "69c80c1cba7d5c37", "metadata": {}, "source": ["First, index the codebase in a vector store.\n", "\n", "Set `repo_dir` to the path of the repository you want to index."]}, {"cell_type": "code", "execution_count": 1, "id": "b9dd5259592cc65e", "metadata": {"ExecuteTime": {"end_time": "2024-06-17T05:14:44.351498Z", "start_time": "2024-06-17T05:14:22.983524Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found existing index on /tmp/moatless-tools/.index\n"]}], "source": ["from moatless.index import CodeIndex, IndexSettings\n", "from moatless.repository import FileRepository\n", "from moatless.workspace import Workspace\n", "\n", "import os\n", "import logging\n", "from dotenv import load_dotenv\n", "\n", "#logging.basicConfig(level=logging.WARNING)\n", "#logging.getLogger(\"moatless\").setLevel(logging.INFO)\n", "\n", "load_dotenv()\n", "\n", "# An OPENAI_API_KEY is required to use the OpenAI Models\n", "index_settings = IndexSettings(\n", "    embed_model=\"text-embedding-3-small\",\n", ")\n", "\n", "\n", "repo_dir = \"/tmp/moatless-tools\"\n", "persist_dir = \"/tmp/moatless-tools/.index\"\n", "\n", "file_repo = FileRepository(repo_path=repo_dir)\n", "\n", "if os.path.exists(persist_dir):\n", "    print(f\"Found existing index on {persist_dir}\")\n", "    code_index = CodeIndex.from_persist_dir(persist_dir, file_repo=file_repo)\n", "else:\n", "    code_index = CodeIndex(file_repo=file_repo, settings=index_settings)\n", "    nodes, tokens = code_index.run_ingestion()\n", "    print(f\"Indexed {nodes} nodes and {tokens} tokens\")\n", "    code_index.persist(persist_dir)\n", "\n", "workspace = Workspace(\n", "    repository=file_repo,\n", "    code_index=code_index,\n", "    shadow_mode=True # No changes are persisted to the file system\n", ")"]}, {"cell_type": "markdown", "id": "bc13c37fd492267f", "metadata": {}, "source": ["The Code Inspector Agent combines a deterministic language model with specialized code analysis actions to create a read-only code exploration tool.\n", "\n", "The model uses GPT-4.1-mini with temperature set to 0 for consistent, focused responses, while the prompt guides the agent through a systematic workflow - understanding requests, locating relevant code, gathering details, and reporting findings concisely.\n", "\n", "The action toolkit provides all necessary capabilities for thorough code analysis:\n", "* `Think()` enables private reasoning\n", "* `SemanticSearch()` finds relevant code sections using semantic search to the vector store\n", "* `FindClass()` and `FindFunction()` locate specific components\n", "* `FindCodeSnippet()` identifies code patterns\n", "* `ReadFile()` examines file contents\n", "* `Respond()` delivers findings to users"]}, {"cell_type": "code", "execution_count": 2, "id": "38c6f7f6422053fb", "metadata": {"ExecuteTime": {"end_time": "2024-06-17T05:14:47.217658Z", "start_time": "2024-06-17T05:14:44.355086Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The agent implementation is primarily in the class `ActionAgent` located in `moatless/agent/agent.py`.\n", "\n", "### Description of `ActionAgent`:\n", "- Subclass of `MoatlessComponent`.\n", "- Manages a set of `Action` instances, a `BaseCompletionModel` for generating completions, and a `BaseMemory` for message history.\n", "- Has attributes like `agent_id`, `model_id`, `description`, `system_prompt`, and internal mappings for actions.\n", "- Initialization sets up the completion model with response schemas derived from the actions.\n", "- Provides an async `run` method to process a `Node`, generating and executing action steps.\n", "- Generates actions by creating messages from memory and workspace context, then calling the completion model.\n", "- Executes action steps by mapping action arguments to actual `Action` instances and running them.\n", "- Emits events such as `RunAgentEvent`, `ActionCreatedEvent`, `ActionExecutedEvent`, and `AgentErrorEvent` during its lifecycle.\n", "- Supports workspace setting and initialization of actions with the workspace.\n", "- Uses OpenTelemetry tracing for key methods.\n", "- Provides a `model_dump` method for serialization.\n", "\n", "### Dependencies:\n", "- Python standard libraries: `datetime`, `logging`, `traceback`, `collections.abc`, `typing`.\n", "- Third-party libraries: `pydantic` for data validation and modeling, `opentelemetry` for tracing.\n", "- Internal modules from `moatless` package:\n", "  - `moatless.actions.action` and `moatless.actions.schema` for action definitions.\n", "  - `moatless.agent.events` for agent-related event classes.\n", "  - `moatless.completion` and `moatless.completion.schema` for completion model interfaces.\n", "  - `moatless.component` for base component class.\n", "  - `moatless.context_data` for context management.\n", "  - `moatless.events` for base event class.\n", "  - `moatless.exceptions` for error handling.\n", "  - `moatless.message_history` for memory and message history generation.\n", "  - `moatless.node` for node and action step data structures.\n", "  - `moatless.workspace` for workspace context.\n", "\n", "If you want, I can provide specific code excerpts or more details on any part of this implementation.\n"]}], "source": ["from moatless.actions import ReadFile, FindClass, FindFunction, FindCodeSnippet, StringReplace, CreateFile, Finish, SemanticSearch\n", "from moatless.actions.respond import Respond\n", "from moatless.actions.think import Think\n", "from moatless.completion import ToolCallCompletionModel\n", "from moatless.flow.loop import AgenticLoop\n", "from moatless.message_history import MessageHistoryGenerator\n", "from moatless.agent import ActionAgent\n", "\n", "completion_model = ToolCallCompletionModel(\n", "    model=\"gpt-4.1-mini\",\n", "    temperature=0.0,\n", "    model_api_key=\"\"\n", ")\n", "\n", "system_prompt = \"\"\"# Code Inspector Agent Prompt\n", "\n", "You are a specialized AI agent whose sole purpose is to explore a codebase and report findings. You cannot modify any code or communicate directly with the user—only return information based on built-in inspection functions.\n", "\n", "## 1. <PERSON> Reasoning\n", "- Before starting any inspection task or when facing ambiguity, record a private chain of thought—do not expose it to the user.\n", "\n", "## 2. Investigation Workflow\n", "1. **Understand Request**  \n", "   Read the user’s query to determine which parts of the codebase to inspect.  \n", "2. **Locate Code**  \n", "   Use code-search functions to find relevant files, classes, functions, or snippets.  \n", "3. **<PERSON><PERSON> Details**  \n", "   Open and read the identified code to extract requested information (signatures, dependencies, comments, complexity, etc.).  \n", "4. **Report Findings**  \n", "   Summarize the results clearly and concisely, quoting file paths and code excerpts as needed. Use the `Respond` tool to return the findings.\n", "\n", "## 3. Guidelines\n", "- **Read-Only**: Do not alter or write any files—only inspect and report.  \n", "- **Precision**: Provide exact file names, line numbers, and code snippets.  \n", "- **Context**: When relevant, include surrounding code or module relationships.  \n", "- **Brevity**: Keep responses focused on the user’s question.  \n", "- **Tool Usage**: Always use tools, respond by using the `Respond` tool.\n", "\"\"\"\n", "\n", "agent = ActionAgent(\n", "    completion_model=completion_model,\n", "    system_prompt=system_prompt,\n", "    actions=[\n", "        Think(),\n", "        Seman<PERSON>Sear<PERSON>(),\n", "        FindClass(),\n", "        FindFunction(),\n", "        FindCodeSnippet(),\n", "        ReadFile(),\n", "        <PERSON><PERSON><PERSON>(),\n", "    ],\n", "    memory=MessageHistoryGenerator(),\n", ")\n", "\n", "loop = AgenticLoop.create(agent=agent, max_iterations=20)\n", "\n", "last_node = await loop.run(\"Return a detailed description of the agent implementation and what dependencies it has\", workspace=workspace)\n", "print(last_node.observation.message)\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 5}