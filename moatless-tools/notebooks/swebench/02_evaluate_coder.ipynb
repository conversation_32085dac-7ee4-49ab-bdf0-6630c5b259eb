{"cells": [{"metadata": {"ExecuteTime": {"end_time": "2024-06-16T10:24:02.340929Z", "start_time": "2024-06-16T10:24:02.331084Z"}}, "cell_type": "code", "source": ["import litellm\n", "import datetime\n", "import os\n", "\n", "index_store_dir = f\"/home/<USER>/index_store\"\n", "repo_base_dir = f\"/tmp/repos\"\n", "\n", "previous_evaluation = \"20240615_moatless_search_gpt4o\"\n", "\n", "model = \"gpt-4o-2024-05-13\"\n", "edit_model = \"gpt-4o-2024-05-13\"\n", "\n", "date_str = datetime.datetime.now().strftime(\"%Y%m%d\")\n", "model_file_name = f\"{model.replace('/', '_')}\"\n", "\n", "if model != edit_model:\n", "    model_file_name += f\"_edit_2_{edit_model.replace('/', '_')}\"\n", "\n", "evaluations_dir = \"/home/<USER>/repos/albert/moatless/evaluations\"\n", "evaluation_name = f\"{date_str}_moatless_code_2_{model_file_name}\"\n", "evaluation_dir = f\"{evaluations_dir}/{evaluation_name}\"\n", "trajectory_dir = f\"{evaluations_dir}/{evaluation_name}/trajs\"\n", "predictions_path = f\"{evaluation_dir}/all_preds.jsonl\"\n", "\n", "previous_trajectories_dir = f\"{evaluations_dir}/{previous_evaluation}/trajs\"\n", "\n", "if not os.path.exists(trajectory_dir):\n", "    os.makedirs(trajectory_dir)\n", "\n", "litellm.success_callback = [\"langfuse\"]\n", "litellm.failure_callback = [\"langfuse\"]"], "id": "20ddba631a73df13", "execution_count": 9, "outputs": []}, {"metadata": {"ExecuteTime": {"end_time": "2024-06-16T10:24:02.359925Z", "start_time": "2024-06-16T10:24:02.343486Z"}}, "cell_type": "code", "source": ["def determine_status(info: dict) -> str:\n", "    if \"error\" in info:\n", "        return \"error\"\n", "    \n", "    if \"submission\" not in info or not info[\"submission\"]:\n", "        return \"not_submitted\"\n", "    \n", "    prediction = info[\"submission\"]\n", "    \n", "    result_file = f\"{evaluation_dir}/result.json\"\n", "    if not os.path.exists(result_file) and prediction:\n", "        # No support for evaluation yet. Generate the swe bench evaluation result file and run again...\n", "        return \"generated\"\n", "    \n", "    with open(os.path.join(result_file), \"r\") as f:\n", "        report = json.load(f)\n", "        \n", "    if info[\"instance_id\"] in report[\"resolved\"]:\n", "        return \"resolved\"\n", "    else:\n", "        return \"failed\"\n", "\n", "def to_result(trajectory: dict, instance: dict) -> dict:\n", "    info = trajectory[\"info\"]\n", "    \n", "    result = {\n", "        \"instance_id\": info[\"instance_id\"],\n", "        \"duration\": info[\"duration\"],\n", "        \"total_cost\": info[\"total_cost\"],\n", "        \"status\": determine_status(info),\n", "        #\"transitions\": len(trajectory[\"transitions\"]),\n", "        \"rejections\": 0,\n", "        \"changes\": 0,\n", "        \"resolved_by\": len(instance[\"resolved_by\"])\n", "    }\n", "\n", "    return result"], "id": "611e5d6d74f07ecf", "execution_count": 10, "outputs": []}, {"metadata": {"ExecuteTime": {"end_time": "2024-06-16T10:24:02.378186Z", "start_time": "2024-06-16T10:24:02.362397Z"}}, "cell_type": "code", "source": ["from moatless.edit.edit import EditCode\n", "from moatless.workspace import Workspace\n", "import subprocess\n", "\n", "from moatless.transitions import code_transitions\n", "from moatless.loop import AgenticLoop\n", "import traceback\n", "from moatless.evaluation.utils import trace_metadata\n", "import logging\n", "import os\n", "import time\n", "from moatless.benchmark.swebench import setup_swebench_repo, get_repo_dir_name, sync_file_context_with_search_trajectory\n", "\n", "\n", "def evaluate(instance):\n", "    repo_dir = setup_swebench_repo(instance)\n", "    instance_id = instance[\"instance_id\"]\n", "    persist_dir = os.path.join(\n", "        index_store_dir, get_repo_dir_name(instance[\"instance_id\"])\n", "    )\n", "    \n", "    workspace = Workspace.from_dirs(repo_dir=repo_dir, index_dir=persist_dir)\n", "\n", "    trajectory_path = os.path.join(trajectory_dir, f\"{instance['instance_id']}.json\")\n", "    if os.path.exists(trajectory_path):\n", "        with open(trajectory_path) as file:\n", "            trajectory = json.load(file)\n", "        if \"info\" in trajectory and trajectory[\"info\"].get(\"submission\"):\n", "            return trajectory\n", "    \n", "    previous_trajectory_file = os.path.join(previous_trajectories_dir, f\"{instance['instance_id']}.json\")\n", "    if os.path.exists(previous_trajectory_file):\n", "        with open(previous_trajectory_file) as file:\n", "            previous_trajectory = json.load(file)\n", "        sync_file_context_with_search_trajectory(workspace, previous_trajectory)\n", "    else:\n", "        print(f\"Missing previous trajectory file {previous_trajectory_file}\")\n", "        return None\n", "\n", "    if not workspace.file_context.files:\n", "        print(f\"No files in context from previous trajectory for {instance['instance_id']}\")\n", "        return None\n", "    \n", "    metadata = trace_metadata(instance_id=instance_id, session_id=evaluation_name, trace_name=\"code\")\n", "    transitions = code_transitions(global_params={\"model\": model}, state_params={EditCode: {\"model\": edit_model}})\n", "    \n", "    code_loop = AgenticLoop(transitions=transitions, workspace=workspace, metadata=metadata, trajectory_path=trajectory_path, max_cost=0.5)\n", "\n", "    info = {\n", "        \"evaluation_name\": evaluation_name,\n", "        \"instance_id\": instance[\"instance_id\"]\n", "    }\n", "    \n", "    start_time = time.time()\n", "    try:\n", "        response = code_loop.run(message=instance[\"problem_statement\"])\n", "    except Exception as e:\n", "        info[\"error\"] = traceback.format_exc()\n", "        logging.exception(f\"Error in evaluation of {instance['instance_id']} \")\n", "        raise e\n", "\n", "    info[\"duration\"] = time.time() - start_time\n", "    info[\"total_cost\"] = code_loop.trajectory.total_cost()\n", "    \n", "    workspace.save()\n", "    \n", "    output = subprocess.run(\n", "          [\"git\", \"diff\"],\n", "          capture_output=True,\n", "          text=True,\n", "          cwd=repo_dir,\n", "    )\n", "    \n", "    info[\"submission\"] = output.stdout\n", "\n", "    code_loop.trajectory.save_info(info)\n", "    \n", "    return code_loop.trajectory.to_dict()\n"], "id": "a476221019e723c6", "execution_count": 11, "outputs": []}, {"metadata": {"ExecuteTime": {"end_time": "2024-06-16T10:26:03.723822Z", "start_time": "2024-06-16T10:24:02.381772Z"}}, "cell_type": "code", "source": ["from moatless.benchmark.swebench import generate_md_report\n", "from pandas import DataFrame\n", "import pandas as pd\n", "from tqdm.notebook import tqdm\n", "import json\n", "\n", "def run_evaluation(dataset_file: str) -> DataFrame:\n", "    count = 0\n", "    generated = 0\n", "    error = 0\n", "    \n", "    sum_duration = 0\n", "    sum_total_cost = 0\n", "    \n", "    with open(dataset_file, \"r\") as f:\n", "        instances = json.load(f)\n", "\n", "    with open(predictions_path, \"w\") as file:\n", "        file.write(\"\")\n", "\n", "    results = []\n", "    instances = [instance for instance in instances if len(instance[\"resolved_by\"]) >= 6]\n", "    \n", "    instances = [instance for instance in instances if instance[\"instance_id\"] in [\"django__django-15789\",\"django__django-15851\",\"pytest-dev__pytest-5692\",\"scikit-learn__scikit-learn-13241\",\"scikit-learn__scikit-learn-13779\",\"scikit-learn__scikit-learn-25570\"]]\n", "\n", "    stats = {}\n", "    pbar = tqdm(instances)\n", "    for instance in pbar:\n", "        pbar.set_description(f\"Instance {instance['instance_id']}\")\n", "        \n", "        trajectory = evaluate(instance)\n", "        if not trajectory:\n", "            continue\n", "    \n", "        result = to_result(trajectory, instance)\n", "        results.append(result)\n", "        \n", "        try:\n", "            if result[\"status\"] in [\"failed\"]:\n", "                md_report = generate_md_report(trajectory, instance)\n", "                if not os.path.exists(f\"{evaluation_dir}/reports\"):\n", "                    os.makedirs(f\"{evaluation_dir}/reports\")\n", "                with open(f\"{evaluation_dir}/reports/{instance['instance_id']}.md\", \"w\") as file:\n", "                    file.write(md_report)\n", "        except Exception as e:\n", "            logging.exception(f\"Error in generating report for {instance['instance_id']} \")\n", "        \n", "        sum_duration += result[\"duration\"]\n", "        sum_total_cost += result[\"total_cost\"]\n", "        \n", "        if result[\"status\"] == \"error\":\n", "            error += 1\n", "\n", "        if result[\"status\"] in [\"generated\", \"failed\", \"resolved\"]:\n", "            generated += 1\n", "        \n", "        count += 1\n", "\n", "        if sum_duration > 0:\n", "            stats[\"avg_duration\"] = sum_duration / count\n", "\n", "        if sum_total_cost > 0:\n", "            stats[\"avg_cost\"] = sum_total_cost / count\n", "            stats[\"total_cost\"] = sum_total_cost\n", "        \n", "        if generated > 0:\n", "            success_rate = (generated / count) * 100\n", "            stats[\"generated\"] = f\"{success_rate:.2f}%\"\n", "    \n", "        stats[\"error\"] = error\n", "        \n", "        pbar.set_postfix(stats)\n", "    \n", "        prediction = {\n", "            \"model_name_or_path\": evaluation_name,\n", "            \"instance_id\": instance[\"instance_id\"],\n", "            \"model_patch\": trajectory[\"info\"].get(\"submission\", \"\"),\n", "        }\n", "    \n", "        with open(predictions_path, \"a\") as file:\n", "            json_string = json.dumps(prediction)\n", "            file.write(json_string + \"\\n\")\n", "        \n", "        df = pd.DataFrame(results)\n", "        df.to_csv(f\"{evaluation_dir}/result.csv\", index=False, sep=';', decimal=',')\n", "\n", "    return pd.DataFrame(results)\n", "\n", "df = run_evaluation(\"/home/<USER>/repos/albert/moatless/datasets/swebench_lite_all_evaluations.json\")"], "id": "ab446dc72a762649", "execution_count": 12, "outputs": []}, {"metadata": {}, "cell_type": "code", "source": ["df.to_csv(f\"{evaluation_dir}/result.csv\", index=False, sep=';', decimal=',')\n", "df"], "id": "c3c420e9bd28dd40", "execution_count": null, "outputs": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}