{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a068fdc332c9a4d8", "metadata": {"ExecuteTime": {"end_time": "2024-06-15T08:35:43.806834Z", "start_time": "2024-06-15T08:35:43.010364Z"}}, "outputs": [], "source": ["import datetime\n", "import os\n", "\n", "index_store_dir = f\"/home/<USER>/index_store\"\n", "repo_base_dir = f\"/tmp/repos\"\n", "\n", "evaluations_dir = \"/home/<USER>/repos/albert/moatless/evaluations\"\n", "evaluation_name = f\"\"\n", "evaluation_dir = f\"{evaluations_dir}/{evaluation_name}\"\n", "trajectory_dir = f\"{evaluations_dir}/{evaluation_name}/trajs\"\n", "\n", "if not os.path.exists(trajectory_dir):\n", "    os.makedirs(trajectory_dir)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "66d6eea0cb4abe8d", "metadata": {"ExecuteTime": {"end_time": "2024-06-15T08:35:45.468948Z", "start_time": "2024-06-15T08:35:43.808190Z"}}, "outputs": [], "source": ["from moatless.find.identify import IdentifyCode\n", "from moatless.find.search import SearchCode\n", "from moatless.transitions import search_transitions\n", "\n", "global_params = {\n", "    \"model\": \"gpt-4o-2024-05-13\",\n", "    \"temperature\": 0.2,\n", "    \"max_tokens\": 2000,\n", "    \"max_prompt_file_tokens\": 8000,\n", "}\n", "\n", "state_params = {\n", "    SearchCode: {\n", "        \"provide_initial_context\": True,\n", "        \"max_search_results\": 75,\n", "        \"initial_context_tokens\": 6000,\n", "        \"initial_search_results\": 100,\n", "        \"initial_context_spans_per_file\": 5,\n", "    },\n", "    IdentifyCode: {\"expand_context\": True},\n", "}\n", "\n", "transitions = search_transitions(\n", "    global_params=global_params,\n", "    state_params=state_params,\n", ")"]}, {"cell_type": "code", "execution_count": 3, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2024-06-15T08:35:45.785579Z", "start_time": "2024-06-15T08:35:45.469888Z"}}, "outputs": [], "source": ["import logging\n", "import traceback\n", "from moatless.transitions import search_transitions\n", "from moatless.loop import AgenticLoop\n", "import time\n", "from moatless.evaluation.utils import trace_metadata\n", "from moatless import Workspace\n", "from moatless.benchmark.swebench import setup_swebench_repo, get_repo_dir_name\n", "\n", "def evaluate(instance):\n", "    repo_dir = setup_swebench_repo(instance)\n", "    instance_id = instance[\"instance_id\"]\n", "    persist_dir = os.path.join(\n", "        index_store_dir, get_repo_dir_name(instance_id)\n", "    )\n", "    workspace = Workspace.from_dirs(repo_dir=repo_dir, index_dir=persist_dir)\n", "    metadata = trace_metadata(instance_id=instance_id, session_id=evaluation_name, trace_name=\"search\")\n", "\n", "    trajectory_path = os.path.join(trajectory_dir, f\"{instance_id}.json\")\n", "    if os.path.exists(trajectory_path):\n", "        with open(trajectory_path) as file:\n", "            trajectory = json.load(file)\n", "        if \"info\" in trajectory:\n", "            \n", "            return to_result(instance, trajectory, workspace)\n", "\n", "    problem_statement = instance[\"problem_statement\"]\n", "    \n", "    search_instructions = f\"\"\"Find the code relevant to solve this issue: \n", "\n", "{problem_statement}\n", "\"\"\"\n", "    \n", "    info = {\n", "        \"evaluation_name\": evaluation_name,\n", "        \"instance_id\": instance_id,\n", "        \"trace_id\": metadata[\"trace_id\"]\n", "    }\n", "    transitions = search_transitions(global_params={\"model\": model})\n", "    search_loop = AgenticLoop(transitions=transitions, workspace=workspace, metadata=metadata, trajectory_path=trajectory_path)\n", "\n", "    start_time = time.time()\n", "    try:\n", "        search_response = search_loop.run(message=search_instructions)\n", "        \n", "    except Exception as e:\n", "        info[\"error\"] = traceback.format_exc()\n", "        logging.exception(f\"Error in evaluation of {instance['instance_id']} \")\n", "  \n", "    info[\"duration\"] = time.time() - start_time\n", "    info[\"total_cost\"] = search_loop.trajectory.total_cost()\n", "    search_loop.trajectory.save_info(info)\n", "    \n", "    return to_result(instance, search_loop.trajectory.to_dict(), workspace)"]}, {"cell_type": "code", "execution_count": 4, "id": "398309b4de44e80d", "metadata": {"ExecuteTime": {"end_time": "2024-06-15T08:35:56.745639Z", "start_time": "2024-06-15T08:35:45.786823Z"}}, "outputs": [], "source": ["from pandas import DataFrame\n", "import pandas as pd\n", "from tqdm.notebook import tqdm\n", "import json\n", "\n", "def run_evaluation(dataset_file: str) -> DataFrame:\n", "    count = 0\n", "    expected_identified = 0\n", "    error = 0\n", "    \n", "    sum_duration = 0\n", "    sum_total_cost = 0\n", "    \n", "    with open(dataset_file, \"r\") as f:\n", "        instances = json.load(f)\n", "\n", "    results = []\n", "    instances = [instance for instance in instances if len(instance[\"resolved_by\"]) >= 6]\n", "    instances = sorted(instances, key=lambda x: x[\"instance_id\"])\n", "\n", "    stats = {}\n", "    pbar = tqdm(instances)\n", "    for instance in pbar:\n", "        pbar.set_description(f\"Instance {instance['instance_id']}\")\n", "        \n", "        result = evaluate(instance)\n", "        if not result:\n", "            continue\n", "    \n", "        results.append(result)\n", "        \n", "        count += 1\n", "        \n", "        if result[\"expected_identified\"] or result[\"alt_identified\"]:\n", "            expected_identified += 1\n", "        \n", "        sum_duration += result[\"duration\"]\n", "        sum_total_cost += result[\"total_cost\"]\n", "\n", "        if sum_duration > 0:\n", "            stats[\"avg_duration\"] = sum_duration / count\n", "\n", "        if sum_total_cost > 0:\n", "            stats[\"avg_cost\"] = sum_total_cost / count\n", "            stats[\"total_cost\"] = sum_total_cost\n", "    \n", "        if expected_identified:\n", "            success_rate = (expected_identified / count) * 100\n", "            stats[\"success_rate\"] = f\"{success_rate:.2f}%\"\n", "    \n", "        stats[\"error\"] = error\n", "        \n", "        pbar.set_postfix(stats)\n", "\n", "    return pd.DataFrame(results)\n", "\n", "df = run_evaluation(\"/home/<USER>/repos/albert/moatless/datasets/swebench_lite_all_evaluations.json\")"]}, {"cell_type": "code", "execution_count": null, "id": "f6f979631e1caf4", "metadata": {"ExecuteTime": {"end_time": "2024-06-15T08:35:56.747100Z", "start_time": "2024-06-15T08:35:56.746889Z"}}, "outputs": [], "source": ["df.to_csv(f\"{evaluation_dir}/result.csv\", index=False, sep=';', decimal=',')\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "f8e931eb46f23d03", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}